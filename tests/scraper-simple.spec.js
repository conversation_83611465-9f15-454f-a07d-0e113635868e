const { test, expect } = require('@playwright/test');

test.describe('Scraper Page', () => {
    test.beforeEach(async ({ page }) => {
        // Login as admin
        await page.goto('/admin/login.php');
        await page.fill('input[name="username"]', 'admin');
        await page.fill('input[name="password"]', 'admin');
        await page.click('button[type="submit"]');
        await page.waitForURL('**/admin/index.php');
    });

    test('should load scraper page and show basic interface', async ({ page }) => {
        console.log('Testing: Scraper page loads and shows basic interface');
        
        // Navigate to scraper page
        await page.goto('/admin/scraper.php');
        
        // Wait for page to load
        await page.waitForLoadState('networkidle');
        
        // Check page title
        await expect(page).toHaveTitle(/News Scraper.*Minimal CMS/);
        
        // Check main heading
        await expect(page.locator('h2')).toContainText('News Scraper Administration');
        
        // Check navigation includes scraper link
        const scraperNav = page.locator('nav.admin-nav a[href="scraper.php"]');
        await expect(scraperNav).toBeVisible();
        await expect(scraperNav).toHaveClass(/active/);
        
        // Check action buttons
        await expect(page.locator('a[href="scraper.php?action=new"]')).toContainText('Add New Configuration');
        await expect(page.locator('a[href="scraper.php?action=help"]')).toContainText('Help & Documentation');
        
        console.log('✅ Scraper page loaded successfully with basic interface');
    });

    test('should display help documentation', async ({ page }) => {
        console.log('Testing: Help documentation displays correctly');
        
        // Navigate to help page
        await page.goto('/admin/scraper.php?action=help');
        
        // Wait for page to load
        await page.waitForLoadState('networkidle');
        
        // Check help content
        await expect(page.locator('.help-section h3')).toContainText('News Scraper Help');
        await expect(page.locator('.help-content')).toContainText('Configuration File Format');
        await expect(page.locator('.help-content')).toContainText('CSS Selectors');
        await expect(page.locator('.help-content code')).toContainText('site_name');
        
        console.log('✅ Help documentation displayed correctly');
    });

    test('should show configuration files if they exist', async ({ page }) => {
        console.log('Testing: Configuration files display correctly');
        
        // Navigate to scraper page
        await page.goto('/admin/scraper.php');
        
        // Wait for page to load
        await page.waitForLoadState('networkidle');
        
        // Check if configurations section exists
        const configsSection = page.locator('.configs-section');
        await expect(configsSection).toBeVisible();
        
        // Should show either config files or no content message
        const hasConfigs = await page.locator('.config-card').count() > 0;
        const hasNoContent = await page.locator('.no-content').isVisible();
        
        expect(hasConfigs || hasNoContent).toBeTruthy();
        
        if (hasConfigs) {
            console.log('✅ Configuration files displayed');
        } else {
            console.log('✅ No configuration files message displayed');
        }
    });

    test('should show new configuration form', async ({ page }) => {
        console.log('Testing: New configuration form displays correctly');
        
        // Navigate to new configuration page
        await page.goto('/admin/scraper.php?action=new');
        
        // Wait for page to load
        await page.waitForLoadState('networkidle');
        
        // Check form elements
        await expect(page.locator('h3')).toContainText('Create New Configuration');
        await expect(page.locator('input[name="filename"]')).toBeVisible();
        await expect(page.locator('input[name="site_name"]')).toBeVisible();
        await expect(page.locator('input[name="source_url"]')).toBeVisible();
        await expect(page.locator('input[name="title_selector"]')).toBeVisible();
        await expect(page.locator('input[name="url_selector"]')).toBeVisible();
        await expect(page.locator('input[name="thumbnail_selector"]')).toBeVisible();
        
        // Check form buttons
        await expect(page.locator('button[value="create"]')).toContainText('Create Configuration');
        await expect(page.locator('button[value="test"]')).toContainText('Test Configuration');
        await expect(page.locator('a[href="scraper.php"]')).toContainText('Cancel');
        
        console.log('✅ New configuration form displayed correctly');
    });

    test('should validate form input', async ({ page }) => {
        console.log('Testing: Form validation works correctly');
        
        // Navigate to new configuration page
        await page.goto('/admin/scraper.php?action=new');
        
        // Wait for page to load
        await page.waitForLoadState('networkidle');
        
        // Try to submit empty form
        await page.click('button[value="create"]');
        
        // Check that required field validation works
        const filenameField = page.locator('input[name="filename"]');
        const siteNameField = page.locator('input[name="site_name"]');
        const sourceUrlField = page.locator('input[name="source_url"]');
        
        // HTML5 validation should prevent submission
        await expect(filenameField).toHaveAttribute('required');
        await expect(siteNameField).toHaveAttribute('required');
        await expect(sourceUrlField).toHaveAttribute('required');
        
        console.log('✅ Form validation working correctly');
    });

    test('should navigate between admin pages successfully', async ({ page }) => {
        console.log('Testing: Navigation between admin pages works');
        
        // Start at scraper page
        await page.goto('/admin/scraper.php');
        await page.waitForLoadState('networkidle');
        
        // Test navigation to other admin pages
        await page.click('nav.admin-nav a[href="index.php"]');
        await page.waitForURL('**/admin/index.php');
        await expect(page.locator('h2')).toContainText('Welcome back');
        
        // Navigate back to scraper
        await page.click('nav.admin-nav a[href="scraper.php"]');
        await page.waitForURL('**/admin/scraper.php');
        await expect(page.locator('h2')).toContainText('News Scraper Administration');
        
        console.log('✅ Navigation between admin pages working correctly');
    });
});
