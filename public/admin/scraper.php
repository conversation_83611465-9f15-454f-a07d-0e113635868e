<?php
/**
 * News Scraper Administration Page
 * Manage YAML configuration files and test web scraping functionality
 */

if (!defined("CMS_INIT")) {
    define("CMS_INIT", true);
    require_once "../../src/includes/init.php";
}

// Require authentication
$auth->requireAuth();

// Include shared components
require_once "includes/header.php";
require_once "includes/footer.php";

$currentUser = $auth->getCurrentUser();

// Handle form submissions
$action = $_GET['action'] ?? 'list';

try {
    // Get YAML configuration files from news_src directory
    $newsSrcPath = dirname(dirname(__DIR__)) . '/news_src';
    $configFiles = [];
    
    if (is_dir($newsSrcPath)) {
        $files = scandir($newsSrcPath);
        foreach ($files as $file) {
            if (pathinfo($file, PATHINFO_EXTENSION) === 'yaml' || pathinfo($file, PATHINFO_EXTENSION) === 'yml') {
                $configFiles[] = $file;
            }
        }
    }
    
} catch (Exception $e) {
    error_log("Error loading scraper configurations: " . $e->getMessage());
    $configFiles = [];
    addFlashMessage("Error loading scraper configurations.", "error");
}

// Render header
renderAdminHeader('News Scraper');
?>

<div class="page-header">
    <h2>News Scraper Administration</h2>
    <div class="page-actions">
        <a href="scraper.php?action=new" class="button button-primary">
            Add New Configuration
        </a>
        <a href="scraper.php?action=help" class="button">
            Help & Documentation
        </a>
    </div>
</div>

<?php if ($action === 'help'): ?>
    <div class="help-section">
        <h3>News Scraper Help</h3>
        <div class="help-content">
            <h4>Configuration File Format</h4>
            <p>Create YAML files in the <code>news_src/</code> directory with the following structure:</p>
            <pre><code>site_name: "Website Name"
source_url: "https://example.com/news"
selectors:
  title: "CSS selector for article titles"
  article_url: "CSS selector for article URLs"
  thumbnail: "CSS selector for article thumbnails" # optional</code></pre>
            
            <h4>CSS Selectors</h4>
            <p>Use standard CSS selectors to target elements:</p>
            <ul>
                <li><code>.class-name</code> - Select by class</li>
                <li><code>#id-name</code> - Select by ID</li>
                <li><code>tag-name</code> - Select by tag</li>
                <li><code>tag.class</code> - Combine selectors</li>
                <li><code>parent > child</code> - Direct child selector</li>
            </ul>
            
            <h4>Testing</h4>
            <p>Use the "Test Configuration" button to verify your selectors work correctly before saving.</p>
        </div>
    </div>

<?php elseif ($action === 'new'): ?>
    <div class="config-form-section">
        <h3>Create New Configuration</h3>
        <form method="post" action="scraper.php" class="config-form">
            <div class="form-group">
                <label for="filename">Configuration Filename:</label>
                <input type="text" id="filename" name="filename" required 
                       placeholder="example.yaml" pattern="[a-zA-Z0-9_-]+\.ya?ml$">
                <small>Must end with .yaml or .yml</small>
            </div>
            
            <div class="form-group">
                <label for="site_name">Site Name:</label>
                <input type="text" id="site_name" name="site_name" required 
                       placeholder="Example News Site">
            </div>
            
            <div class="form-group">
                <label for="source_url">Source URL:</label>
                <input type="url" id="source_url" name="source_url" required 
                       placeholder="https://example.com/news">
            </div>
            
            <div class="form-group">
                <label for="title_selector">Title Selector:</label>
                <input type="text" id="title_selector" name="title_selector" required 
                       placeholder=".article-title, h2.title">
            </div>
            
            <div class="form-group">
                <label for="url_selector">Article URL Selector:</label>
                <input type="text" id="url_selector" name="url_selector" required 
                       placeholder=".article-link, a.title-link">
            </div>
            
            <div class="form-group">
                <label for="thumbnail_selector">Thumbnail Selector (Optional):</label>
                <input type="text" id="thumbnail_selector" name="thumbnail_selector" 
                       placeholder=".article-image img, .thumbnail">
            </div>
            
            <div class="form-actions">
                <button type="submit" name="action" value="create" class="button button-primary">
                    Create Configuration
                </button>
                <button type="submit" name="action" value="test" class="button">
                    Test Configuration
                </button>
                <a href="scraper.php" class="button button-secondary">Cancel</a>
            </div>
        </form>
    </div>

<?php else: ?>
    <!-- Configuration Files List -->
    <div class="configs-section">
        <h3>Configuration Files</h3>
        
        <?php if (empty($configFiles)): ?>
            <div class="no-content">
                <p>No configuration files found in the <code>news_src/</code> directory.</p>
                <p><a href="scraper.php?action=new">Create your first configuration</a></p>
            </div>
        <?php else: ?>
            <div class="configs-grid">
                <?php foreach ($configFiles as $file): ?>
                    <div class="config-card">
                        <div class="config-header">
                            <h4><?php echo Security::escape($file); ?></h4>
                            <div class="config-actions">
                                <a href="scraper.php?action=view&file=<?php echo urlencode($file); ?>" 
                                   class="button button-small">View</a>
                                <a href="scraper.php?action=test&file=<?php echo urlencode($file); ?>" 
                                   class="button button-small">Test</a>
                                <a href="scraper.php?action=edit&file=<?php echo urlencode($file); ?>" 
                                   class="button button-small">Edit</a>
                            </div>
                        </div>
                        
                        <div class="config-info">
                            <?php
                            try {
                                $filePath = $newsSrcPath . '/' . $file;
                                if (file_exists($filePath)) {
                                    $content = file_get_contents($filePath);
                                    // Basic YAML parsing for display
                                    if (preg_match('/site_name:\s*["\']?([^"\'\n]+)["\']?/', $content, $matches)) {
                                        echo '<p><strong>Site:</strong> ' . Security::escape(trim($matches[1])) . '</p>';
                                    }
                                    if (preg_match('/source_url:\s*["\']?([^"\'\n]+)["\']?/', $content, $matches)) {
                                        echo '<p><strong>URL:</strong> ' . Security::escape(trim($matches[1])) . '</p>';
                                    }
                                }
                            } catch (Exception $e) {
                                echo '<p class="error">Error reading file</p>';
                            }
                            ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
<?php endif; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('[scraper] start - initializing scraper administration page');
    
    // Add any JavaScript functionality here
    
    console.log('[scraper] end - scraper page initialized successfully');
});
</script>

<?php
// Render footer
renderAdminFooter();
?>
