<?php
/**
 * Media Management
 * Handles listing and management of uploaded media files
 */

if (!defined("CMS_INIT")) {
    define("CMS_INIT", true);
    require_once "../../src/includes/init.php";
}

// Require authentication
$auth->requireAuth();

$currentUser = $auth->getCurrentUser();

// Handle actions (delete/remove)
if ($_SERVER["REQUEST_METHOD"] === "POST" && isset($_POST["action"])) {
    try {
        validateCSRF();

        if ($_POST["action"] === "delete_local" && isset($_POST["file_path"])) {
            $filePath = $_POST["file_path"];
            $fullPath = __DIR__ . "/../" . ltrim($filePath, "/");

            // Security check: ensure file is in allowed directories
            $allowedDirs = ["uploads/media/", "uploads/thumbnails/"];
            $isAllowed = false;
            foreach ($allowedDirs as $dir) {
                if (strpos($filePath, $dir) === 1) {
                    // Check for /uploads/media/ or /uploads/thumbnails/
                    $isAllowed = true;
                    break;
                }
            }

            if ($isAllowed && file_exists($fullPath) && is_file($fullPath)) {
                if (unlink($fullPath)) {
                    logActivity(
                        "media_deleted",
                        "Deleted local file: {$filePath}",
                        $currentUser["id"]
                    );
                    addFlashMessage("File deleted successfully!", "success");
                } else {
                    addFlashMessage("Failed to delete file.", "error");
                }
            } else {
                addFlashMessage("File not found or access denied.", "error");
            }
        } elseif (
            $_POST["action"] === "delete_remote" &&
            isset($_POST["image_url"])
        ) {
            // Permanently remove remote image URL from post content
            $imageUrl = $_POST["image_url"];
            $db = Database::getInstance();
            $pdo = $db->getPdo();

            $pdo->beginTransaction();

            try {
                // Get all posts that might contain this image
                $posts = $pdo
                    ->query(
                        "SELECT id, title, content, thumbnail_url FROM posts"
                    )
                    ->fetchAll(PDO::FETCH_ASSOC);
                $updatedPosts = 0;

                foreach ($posts as $post) {
                    $originalContent = $post["content"];
                    $originalThumbnail = $post["thumbnail_url"];
                    $needsUpdate = false;

                    // Remove from content (markdown and HTML images)
                    $newContent = $originalContent;

                    // Remove markdown images: ![alt](url)
                    $newContent = preg_replace(
                        "/!\[[^\]]*\]\(" . preg_quote($imageUrl, "/") . "\)/",
                        "",
                        $newContent
                    );

                    // Remove HTML images: <img src="url">
                    $newContent = preg_replace(
                        '/<img[^>]+src=["\']' .
                            preg_quote($imageUrl, "/") .
                            '["\'][^>]*>/i',
                        "",
                        $newContent
                    );

                    // Clean up extra whitespace and empty lines
                    $newContent = preg_replace(
                        '/\n\s*\n\s*\n/',
                        "\n\n",
                        $newContent
                    );
                    $newContent = trim($newContent);

                    if ($newContent !== $originalContent) {
                        $needsUpdate = true;
                    }

                    // Remove from thumbnail if it matches
                    $newThumbnail = $originalThumbnail;
                    if ($originalThumbnail === $imageUrl) {
                        $newThumbnail = null;
                        $needsUpdate = true;
                    }

                    // Update the post if changes were made
                    if ($needsUpdate) {
                        $updateStmt = $pdo->prepare(
                            "UPDATE posts SET content = ?, thumbnail_url = ? WHERE id = ?"
                        );
                        $updateStmt->execute([
                            $newContent,
                            $newThumbnail,
                            $post["id"],
                        ]);
                        $updatedPosts++;

                        logActivity(
                            "remote_image_deleted",
                            "Removed remote image {$imageUrl} from post: {$post["title"]}",
                            $currentUser["id"]
                        );
                    }
                }

                $pdo->commit();

                if ($updatedPosts > 0) {
                    addFlashMessage(
                        "Remote image permanently removed from {$updatedPosts} post(s).",
                        "success"
                    );
                } else {
                    addFlashMessage(
                        "No posts found containing this image.",
                        "info"
                    );
                }
            } catch (Exception $e) {
                $pdo->rollback();
                throw $e;
            }
        }

        redirect("media.php");
    } catch (Exception $e) {
        addFlashMessage("Action failed: " . $e->getMessage(), "error");
        redirect("media.php");
    }
}

// Function to get file size in human readable format
function formatFileSize($bytes)
{
    if ($bytes === 0) {
        return "0 B";
    }

    $units = ["B", "KB", "MB", "GB"];
    $power = floor(log($bytes, 1024));
    $power = min($power, count($units) - 1);

    return round($bytes / pow(1024, $power), 2) . " " . $units[$power];
}

// Function to scan directory for media files
function getMediaFiles($directory, $webPath)
{
    $files = [];
    $fullPath = __DIR__ . "/../" . $directory;

    if (!is_dir($fullPath)) {
        return $files;
    }

    $allowedExtensions = ["jpg", "jpeg", "png", "gif", "webp", "bmp", "svg"];

    foreach (scandir($fullPath) as $file) {
        if ($file === "." || $file === "..") {
            continue;
        }

        $filePath = $fullPath . "/" . $file;
        if (!is_file($filePath)) {
            continue;
        }

        $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
        if (!in_array($extension, $allowedExtensions)) {
            continue;
        }

        $files[] = [
            "name" => $file,
            "path" => $webPath . "/" . $file,
            "full_path" => $filePath,
            "size" => filesize($filePath),
            "modified" => filemtime($filePath),
            "extension" => $extension,
            "directory" => $directory,
            "type" => "local",
        ];
    }

    return $files;
}

// Function to extract remote images from post content
function getRemoteImages()
{
    $files = [];

    try {
        $db = Database::getInstance();
        $pdo = $db->getPdo();

        // Get all posts with content and thumbnail URLs
        $stmt = $pdo->prepare(
            "SELECT id, title, content, thumbnail_url, updated_at FROM posts ORDER BY updated_at DESC"
        );
        $stmt->execute();
        $posts = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $processedUrls = []; // Track processed URLs to avoid duplicates

        foreach ($posts as $post) {
            // Extract images from markdown content
            if (!empty($post["content"])) {
                // Pattern to match markdown images: ![alt](url)
                preg_match_all(
                    "/!\[([^\]]*)\]\(([^)]+)\)/",
                    $post["content"],
                    $markdownMatches
                );

                // Pattern to match HTML img tags: <img src="url">
                preg_match_all(
                    '/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i',
                    $post["content"],
                    $htmlMatches
                );

                $imageUrls = array_merge($markdownMatches[2], $htmlMatches[1]);

                foreach ($imageUrls as $url) {
                    // Check if it's a remote URL (starts with http/https)
                    if (
                        preg_match("/^https?:\/\//", $url) &&
                        !isset($processedUrls[$url])
                    ) {
                        $processedUrls[$url] = true;

                        // Get file extension from URL
                        $parsedUrl = parse_url($url);
                        $pathInfo = pathinfo($parsedUrl["path"]);
                        $extension = isset($pathInfo["extension"])
                            ? strtolower($pathInfo["extension"])
                            : "unknown";

                        // Generate a display name
                        $displayName = basename($parsedUrl["path"]);
                        if (empty($displayName) || $displayName === "/") {
                            $displayName = "Remote Image";
                        }

                        $files[] = [
                            "name" => $displayName,
                            "path" => $url,
                            "full_path" => $url,
                            "size" => 0, // Unknown size for remote images
                            "modified" => strtotime($post["updated_at"]),
                            "extension" => $extension,
                            "directory" => "remote",
                            "type" => "remote",
                            "post_id" => $post["id"],
                            "post_title" => $post["title"],
                        ];
                    }
                }
            }

            // Add thumbnail URL if it's remote
            if (
                !empty($post["thumbnail_url"]) &&
                preg_match("/^https?:\/\//", $post["thumbnail_url"]) &&
                !isset($processedUrls[$post["thumbnail_url"]])
            ) {
                $processedUrls[$post["thumbnail_url"]] = true;

                $parsedUrl = parse_url($post["thumbnail_url"]);
                $pathInfo = pathinfo($parsedUrl["path"]);
                $extension = isset($pathInfo["extension"])
                    ? strtolower($pathInfo["extension"])
                    : "unknown";

                $displayName = basename($parsedUrl["path"]);
                if (empty($displayName) || $displayName === "/") {
                    $displayName = "Thumbnail Image";
                }

                $files[] = [
                    "name" => $displayName,
                    "path" => $post["thumbnail_url"],
                    "full_path" => $post["thumbnail_url"],
                    "size" => 0,
                    "modified" => strtotime($post["updated_at"]),
                    "extension" => $extension,
                    "directory" => "remote",
                    "type" => "remote",
                    "post_id" => $post["id"],
                    "post_title" => $post["title"],
                ];
            }
        }
    } catch (Exception $e) {
        error_log("Error extracting remote images: " . $e->getMessage());
    }

    return $files;
}

// Get all media files
$mediaFiles = getMediaFiles("uploads/media", "/uploads/media");
$thumbnailFiles = getMediaFiles("uploads/thumbnails", "/uploads/thumbnails");
$remoteFiles = getRemoteImages();

// Combine all files and sort by date (newest first)
$allFiles = array_merge($mediaFiles, $thumbnailFiles, $remoteFiles);
usort($allFiles, function ($a, $b) {
    return $b["modified"] - $a["modified"];
});

// Calculate total storage usage (only for local files)
$totalSize = 0;
$localFileCount = 0;
$remoteFileCount = 0;

foreach ($allFiles as $file) {
    if ($file["type"] === "local") {
        $totalSize += $file["size"];
        $localFileCount++;
    } else {
        $remoteFileCount++;
    }
}

$pageTitle = "Media Management";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo Session::get("csrf_token"); ?>">
    <title><?php echo Security::escape($pageTitle); ?> - Admin</title>
    <link rel="stylesheet" href="/assets/style.css">
</head>
<body class="admin-page">
    <header class="admin-header">
        <div class="container">
            <h1 class="admin-title">
                <a href="index.php">Admin Dashboard</a>
            </h1>

            <nav class="admin-nav">
                <ul>
                    <li><a href="index.php">Dashboard</a></li>
                    <li><a href="posts.php">Posts</a></li>
                    <li><a href="media.php" class="active">Media</a></li>
                    <li><a href="profile.php">Profile</a></li>
                    <li><a href="api.php">API</a></li>
                    <li><a href="../index.php" target="_blank">View Site</a></li>
                    <li><a href="logout.php">Logout</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="admin-main">
        <div class="container">
            <?php echo displayFlashMessages(); ?>

            <div class="admin-header-section">
                <div class="header-left">
                    <h2>Media Management</h2>
                    <div class="admin-stats">
                        <div class="stat-item">
                            <span class="stat-label">Total Files:</span>
                            <span class="stat-value"><?php echo count(
                                $allFiles
                            ); ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Storage Used:</span>
                            <span class="stat-value"><?php echo formatFileSize(
                                $totalSize
                            ); ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Local Files:</span>
                            <span class="stat-value"><?php echo $localFileCount; ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Remote Images:</span>
                            <span class="stat-value"><?php echo $remoteFileCount; ?></span>
                        </div>
                    </div>
                </div>
            </div>

            <?php if (empty($allFiles)): ?>
                <div class="empty-state">
                    <h3>No Media Files Found</h3>
                    <p>No images have been uploaded yet. Create a new post with images to see them listed here.</p>
                    <a href="posts.php?action=new" class="button">Create New Post</a>
                </div>
            <?php else: ?>
                <div class="media-grid">
                    <?php foreach ($allFiles as $file): ?>
                        <div class="media-item" data-type="<?php echo Security::escape(
                            $file["type"]
                        ); ?>">
                            <div class="media-preview">
                                <?php if (
                                    in_array($file["extension"], [
                                        "jpg",
                                        "jpeg",
                                        "png",
                                        "gif",
                                        "webp",
                                        "bmp",
                                    ])
                                ): ?>
                                    <img src="<?php echo Security::escape(
                                        $file["path"]
                                    ); ?>"
                                         alt="<?php echo Security::escape(
                                             $file["name"]
                                         ); ?>"
                                         loading="lazy"
                                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                    <div class="media-error" style="display: none;">
                                        <span class="error-icon">⚠️</span>
                                        <span class="error-text">Image unavailable</span>
                                    </div>
                                <?php else: ?>
                                    <div class="media-placeholder">
                                        <span class="file-extension"><?php echo strtoupper(
                                            $file["extension"]
                                        ); ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="media-info">
                                <h4 class="media-name" title="<?php echo Security::escape(
                                    $file["name"]
                                ); ?>">
                                    <?php echo Security::escape(
                                        strlen($file["name"]) > 30
                                            ? substr($file["name"], 0, 27) .
                                                "..."
                                            : $file["name"]
                                    ); ?>
                                </h4>

                                <div class="media-details">
                                    <div class="detail-row">
                                        <span class="detail-label">Size:</span>
                                        <span class="detail-value"><?php echo $file[
                                            "type"
                                        ] === "remote"
                                            ? "Unknown"
                                            : formatFileSize(
                                                $file["size"]
                                            ); ?></span>
                                    </div>

                                    <div class="detail-row">
                                        <span class="detail-label">Date:</span>
                                        <span class="detail-value"><?php echo date(
                                            "M j, Y",
                                            $file["modified"]
                                        ); ?></span>
                                    </div>

                                    <div class="detail-row">
                                        <span class="detail-label">Time:</span>
                                        <span class="detail-value"><?php echo date(
                                            "g:i A",
                                            $file["modified"]
                                        ); ?></span>
                                    </div>

                                    <div class="detail-row">
                                        <span class="detail-label">Type:</span>
                                        <span class="detail-value">
                                            <?php if (
                                                $file["type"] === "remote"
                                            ): ?>
                                                <span class="badge badge-remote">Remote</span>
                                            <?php elseif (
                                                $file["directory"] ===
                                                "uploads/media"
                                            ): ?>
                                                <span class="badge badge-primary">Media</span>
                                            <?php else: ?>
                                                <span class="badge badge-secondary">Thumbnail</span>
                                            <?php endif; ?>
                                        </span>
                                    </div>

                                    <?php if ($file["type"] === "remote"): ?>
                                        <div class="detail-row">
                                            <span class="detail-label">Source:</span>
                                            <span class="detail-value" title="<?php echo Security::escape(
                                                $file["post_title"]
                                            ); ?>">
                                                Post: <?php echo Security::escape(
                                                    strlen(
                                                        $file["post_title"]
                                                    ) > 20
                                                        ? substr(
                                                                $file[
                                                                    "post_title"
                                                                ],
                                                                0,
                                                                17
                                                            ) . "..."
                                                        : $file["post_title"]
                                                ); ?>
                                            </span>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="media-actions">
                                    <button class="action-btn copy-btn"
                                            data-path="<?php echo Security::escape(
                                                $file["path"]
                                            ); ?>"
                                            title="Copy URL to clipboard">
                                        📋 Copy URL
                                    </button>

                                    <a href="<?php echo Security::escape(
                                        $file["path"]
                                    ); ?>"
                                       target="_blank"
                                       class="action-btn view-btn"
                                       title="Open in new tab">
                                        👁️ View
                                    </a>

                                    <?php if ($file["type"] === "local"): ?>
                                        <button class="action-btn delete-btn"
                                                data-path="<?php echo Security::escape(
                                                    $file["path"]
                                                ); ?>"
                                                data-name="<?php echo Security::escape(
                                                    $file["name"]
                                                ); ?>"
                                                title="Delete file permanently">
                                            🗑️ Delete
                                        </button>
                                    <?php else: ?>
                                        <button class="action-btn delete-remote-btn"
                                                data-url="<?php echo Security::escape(
                                                    $file["path"]
                                                ); ?>"
                                                data-name="<?php echo Security::escape(
                                                    $file["name"]
                                                ); ?>"
                                                title="Delete permanently from posts">
                                            🗑️ Delete
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('[media] start - initializing media management page');

            // Handle copy URL functionality
            const copyButtons = document.querySelectorAll('.copy-btn');
            copyButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const path = this.dataset.path;
                    const fullUrl = window.location.origin + path;

                    // Try to use the modern clipboard API
                    if (navigator.clipboard && window.isSecureContext) {
                        navigator.clipboard.writeText(fullUrl).then(() => {
                            showCopyFeedback(this, 'Copied!');
                        }).catch(() => {
                            fallbackCopyUrl(fullUrl, this);
                        });
                    } else {
                        fallbackCopyUrl(fullUrl, this);
                    }
                });
            });

            function fallbackCopyUrl(text, button) {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.opacity = '0';
                document.body.appendChild(textArea);
                textArea.select();

                try {
                    document.execCommand('copy');
                    showCopyFeedback(button, 'Copied!');
                } catch (err) {
                    showCopyFeedback(button, 'Failed to copy');
                }

                document.body.removeChild(textArea);
            }

            function showCopyFeedback(button, message) {
                const originalText = button.textContent;
                button.textContent = message;
                button.style.backgroundColor = '#28a745';
                button.style.color = 'white';

                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.backgroundColor = '';
                    button.style.color = '';
                }, 2000);
            }

            // Track remote image loading statistics
            const remoteImages = document.querySelectorAll('.media-item[data-type="remote"] img');
            let loadedCount = 0;
            let errorCount = 0;

            remoteImages.forEach(img => {
                img.addEventListener('load', () => {
                    loadedCount++;
                    console.log(`[media] Remote image loaded: ${img.src}`);
                });

                img.addEventListener('error', () => {
                    errorCount++;
                    console.log(`[media] Remote image failed to load: ${img.src}`);
                });
            });

            // Handle delete local file and remove remote image
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('delete-btn')) {
                    const button = e.target;
                    const filePath = button.dataset.path;
                    const fileName = button.dataset.name;

                    if (confirm(`Are you sure you want to permanently delete "${fileName}"? This action cannot be undone.`)) {
                        const form = document.createElement('form');
                        form.method = 'POST';
                        form.innerHTML = `
                            <input type="hidden" name="action" value="delete_local">
                            <input type="hidden" name="file_path" value="${filePath}">
                            <input type="hidden" name="csrf_token" value="${document.querySelector('meta[name="csrf-token"]')?.content || ''}">
                        `;
                        document.body.appendChild(form);
                        form.submit();
                    }
                }

                if (e.target.classList.contains('delete-remote-btn')) {
                    const button = e.target;
                    const imageUrl = button.dataset.url;
                    const fileName = button.dataset.name;

                    if (confirm(`Are you sure you want to permanently delete "${fileName}" from all posts? This will remove the image reference from post content and cannot be undone.`)) {
                        const form = document.createElement('form');
                        form.method = 'POST';
                        form.innerHTML = `
                            <input type="hidden" name="action" value="delete_remote">
                            <input type="hidden" name="image_url" value="${imageUrl}">
                            <input type="hidden" name="csrf_token" value="${document.querySelector('meta[name="csrf-token"]')?.content || ''}">
                        `;
                        document.body.appendChild(form);
                        form.submit();
                    }
                }
            });

            console.log(`[media] Monitoring ${remoteImages.length} remote images`);
            console.log('[media] end - media management page initialized');
        });
    </script>

    <style>
        .admin-header-section {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #dee2e6;
        }

        .header-left h2 {
            margin: 0 0 15px 0;
        }

        .header-actions {
            flex-shrink: 0;
            margin-left: 20px;
        }

        .button {
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            border: none;
            cursor: pointer;
            display: inline-block;
        }

        .button-secondary {
            background: #6c757d;
            color: white;
        }

        .button-secondary:hover {
            background: #5a6268;
        }



        .admin-stats {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .stat-item {
            background: #f8f9fa;
            padding: 10px 15px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }

        .stat-label {
            font-size: 13px;
            color: #6c757d;
            margin-right: 5px;
        }

        .stat-value {
            font-weight: 600;
            color: #495057;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }

        .empty-state h3 {
            color: #495057;
            margin-bottom: 10px;
        }

        .empty-state p {
            color: #6c757d;
            margin-bottom: 20px;
        }

        .media-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .media-item {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .media-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .media-preview {
            height: 200px;
            overflow: hidden;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .media-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .media-placeholder {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            background: #e9ecef;
        }

        .file-extension {
            font-size: 24px;
            font-weight: bold;
            color: #6c757d;
        }

        .media-info {
            padding: 15px;
        }

        .media-name {
            margin: 0 0 10px 0;
            font-size: 14px;
            font-weight: 600;
            color: #495057;
        }

        .media-details {
            margin-bottom: 15px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 13px;
        }

        .detail-label {
            color: #6c757d;
            font-weight: 500;
        }

        .detail-value {
            color: #495057;
        }

        .badge {
            font-size: 11px;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: 500;
        }

        .badge-primary {
            background: #007bff;
            color: white;
        }

        .badge-secondary {
            background: #6c757d;
            color: white;
        }

        .badge-remote {
            background: #fd7e14;
            color: white;
        }

        .media-item[data-type="remote"] {
            border-left: 4px solid #fd7e14;
        }

        .media-item[data-type="remote"] .media-preview {
            position: relative;
        }

        .media-item[data-type="remote"] .media-preview::before {
            content: "🌐";
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(253, 126, 20, 0.9);
            color: white;
            padding: 4px 6px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1;
        }

        .media-error {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            background: #f8d7da;
            color: #721c24;
            gap: 8px;
        }

        .error-icon {
            font-size: 24px;
        }

        .error-text {
            font-size: 12px;
            font-weight: 500;
        }

        .media-actions {
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
        }

        .action-btn {
            flex: 1;
            min-width: 70px;
            padding: 5px 8px;
            font-size: 11px;
            border: 1px solid #dee2e6;
            background: white;
            color: #495057;
            text-decoration: none;
            text-align: center;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .action-btn:hover {
            background: #f8f9fa;
            border-color: #adb5bd;
            text-decoration: none;
        }

        .copy-btn:hover {
            background: #e7f3ff;
            border-color: #007bff;
            color: #007bff;
        }

        .view-btn:hover {
            background: #f0f8e7;
            border-color: #28a745;
            color: #28a745;
        }

        .delete-btn:hover {
            background: #f8d7da;
            border-color: #dc3545;
            color: #dc3545;
        }

        .delete-remote-btn:hover {
            background: #f8d7da;
            border-color: #dc3545;
            color: #dc3545;
        }

        @media (max-width: 768px) {
            .admin-header-section {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .admin-stats {
                width: 100%;
                justify-content: space-between;
            }

            .stat-item {
                flex: 1;
                text-align: center;
                min-width: 0;
            }

            .media-grid {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                gap: 15px;
            }
        }

        @media (max-width: 480px) {
            .media-grid {
                grid-template-columns: 1fr;
            }

            .admin-stats {
                flex-direction: column;
                gap: 10px;
            }

            .stat-item {
                text-align: left;
            }
        }
    </style>
</body>
</html>
