<?php
/**
 * Posts Management
 * Handles CRUD operations for blog posts
 */

if (!defined("CMS_INIT")) {
    define("CMS_INIT", true);
    require_once "../../src/includes/init.php";
}

// Require authentication
$auth->requireAuth();

$currentUser = $auth->getCurrentUser();
$action = $_GET["action"] ?? "list";
$postId = isset($_GET["id"]) ? (int) $_GET["id"] : 0;

// Image download functionality for markdown images
function downloadMarkdownImages($content, $postTitle)
{
    $downloadedImages = [];

    // Create upload directory if it doesn't exist
    $uploadDir = __DIR__ . "/../uploads/media/";
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    // Pattern to match markdown images: ![alt](url)
    $pattern = "/!\[([^\]]*)\]\((https?:\/\/[^\s\)]+)\)/i";

    preg_match_all($pattern, $content, $matches, PREG_SET_ORDER);

    foreach ($matches as $match) {
        $fullMatch = $match[0];
        $altText = $match[1];
        $imageUrl = $match[2];

        // Skip if it's already a local image
        if (strpos($imageUrl, "/uploads/media/") !== false) {
            continue;
        }

        try {
            // Download the image
            $imageData = @file_get_contents($imageUrl);
            if ($imageData === false) {
                continue;
            }

            // Get file extension from URL or content type
            $urlParts = parse_url($imageUrl);
            $pathInfo = pathinfo($urlParts["path"]);
            $extension = isset($pathInfo["extension"])
                ? $pathInfo["extension"]
                : "jpg";

            // Validate extension
            $allowedExtensions = ["jpg", "jpeg", "png", "gif", "webp"];
            if (!in_array(strtolower($extension), $allowedExtensions)) {
                $extension = "jpg";
            }

            // Generate filename: date_time_sanitized-title.ext
            $safeTitle = preg_replace("/[^a-zA-Z0-9-_]/", "-", $postTitle);
            $safeTitle = trim($safeTitle, "-");
            $safeTitle = substr($safeTitle, 0, 50); // Limit length

            $timestamp = date("Y-m-d_H-i-s");
            $filename =
                $timestamp .
                "_" .
                $safeTitle .
                "_" .
                uniqid() .
                "." .
                $extension;

            $localPath = $uploadDir . $filename;
            $webPath = "/uploads/media/" . $filename;

            // Save the image
            if (file_put_contents($localPath, $imageData) !== false) {
                // Replace the URL in content
                $newMarkdown = "![" . $altText . "](" . $webPath . ")";
                $content = str_replace($fullMatch, $newMarkdown, $content);

                // Track downloaded image
                $downloadedImages[] = [
                    "original_url" => $imageUrl,
                    "local_path" => $webPath,
                    "filename" => $filename,
                ];
            }
        } catch (Exception $e) {
            // Continue with other images if one fails
            continue;
        }
    }

    return [
        "content" => $content,
        "downloaded_images" => $downloadedImages,
    ];
}

// Handle form submissions
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    try {
        validateCSRF();

        $postAction = $_POST["action"] ?? "";

        switch ($postAction) {
            case "create":
                $title = Security::sanitizeInput($_POST["title"] ?? "");
                $description = Security::sanitizeInput(
                    $_POST["description"] ?? ""
                );
                $content = Security::sanitizeInput($_POST["content"] ?? "");
                $category = Security::sanitizeInput(
                    $_POST["category"] ?? "tech"
                );
                $sources = Security::sanitizeInput($_POST["sources"] ?? "");
                $thumbnailUrl = Security::sanitizeInput(
                    $_POST["thumbnail_url"] ?? ""
                );
                $isDraft = isset($_POST["is_draft"]) ? true : false;

                // Handle image upload if provided
                if (
                    isset($_FILES["thumbnail_image"]) &&
                    $_FILES["thumbnail_image"]["error"] !== UPLOAD_ERR_NO_FILE
                ) {
                    try {
                        $cropData = null;
                        if (
                            isset($_POST["crop_data"]) &&
                            !empty($_POST["crop_data"])
                        ) {
                            $cropData = json_decode($_POST["crop_data"], true);
                        }
                        $uploadedImagePath = $imageUpload->uploadImage(
                            $_FILES["thumbnail_image"],
                            $cropData
                        );
                        $thumbnailUrl = $uploadedImagePath;
                        logActivity(
                            "image_uploaded",
                            "Uploaded thumbnail for new post: " . $title,
                            $currentUser["id"]
                        );
                    } catch (Exception $e) {
                        addFlashMessage(
                            "Image upload failed: " . $e->getMessage(),
                            "error"
                        );
                        redirect("posts.php?action=new");
                    }
                }

                // Download markdown images and update content
                $imageDownloadResult = downloadMarkdownImages($content, $title);
                $content = $imageDownloadResult["content"];
                $downloadedImages = $imageDownloadResult["downloaded_images"];

                $newPostId = $postManager->createPost(
                    $title,
                    $content,
                    $isDraft,
                    $currentUser["id"],
                    $category,
                    $thumbnailUrl,
                    $description,
                    $sources
                );

                logActivity(
                    "post_created",
                    "Created post: {$title}",
                    $currentUser["id"]
                );

                $successMessage = "Post created successfully!";

                // Add image download confirmation messages
                if (!empty($downloadedImages)) {
                    foreach ($downloadedImages as $image) {
                        $successMessage .=
                            "<br>Downloaded: " .
                            htmlspecialchars($image["original_url"]) .
                            " → " .
                            htmlspecialchars($image["local_path"]);
                    }
                }

                addFlashMessage($successMessage, "success");
                redirect("posts.php?action=edit&id=" . $newPostId);
                break;

            case "update":
                $updatePostId = (int) $_POST["post_id"];
                $title = Security::sanitizeInput($_POST["title"] ?? "");
                $description = Security::sanitizeInput(
                    $_POST["description"] ?? ""
                );
                $content = Security::sanitizeInput($_POST["content"] ?? "");
                $category = Security::sanitizeInput(
                    $_POST["category"] ?? "tech"
                );
                $sources = Security::sanitizeInput($_POST["sources"] ?? "");
                $thumbnailUrl = Security::sanitizeInput(
                    $_POST["thumbnail_url"] ?? ""
                );
                $isDraft = isset($_POST["is_draft"]) ? true : false;

                // Handle image upload if provided
                if (
                    isset($_FILES["thumbnail_image"]) &&
                    $_FILES["thumbnail_image"]["error"] !== UPLOAD_ERR_NO_FILE
                ) {
                    try {
                        $cropData = null;
                        if (
                            isset($_POST["crop_data"]) &&
                            !empty($_POST["crop_data"])
                        ) {
                            $cropData = json_decode($_POST["crop_data"], true);
                        }

                        // Delete old image if it exists and is from uploads
                        $existingPost = $postManager->getPost($updatePostId);
                        if (
                            $existingPost &&
                            !empty($existingPost["thumbnail_url"]) &&
                            strpos(
                                $existingPost["thumbnail_url"],
                                "/uploads/"
                            ) === 0
                        ) {
                            $imageUpload->deleteImage(
                                $existingPost["thumbnail_url"]
                            );
                        }

                        $uploadedImagePath = $imageUpload->uploadImage(
                            $_FILES["thumbnail_image"],
                            $cropData
                        );
                        $thumbnailUrl = $uploadedImagePath;
                        logActivity(
                            "image_uploaded",
                            "Updated thumbnail for post: " . $title,
                            $currentUser["id"]
                        );
                    } catch (Exception $e) {
                        addFlashMessage(
                            "Image upload failed: " . $e->getMessage(),
                            "error"
                        );
                        redirect("posts.php?action=edit&id=" . $updatePostId);
                    }
                }

                // Download markdown images and update content
                $imageDownloadResult = downloadMarkdownImages($content, $title);
                $content = $imageDownloadResult["content"];
                $downloadedImages = $imageDownloadResult["downloaded_images"];

                $postManager->updatePost(
                    $updatePostId,
                    $title,
                    $content,
                    $isDraft,
                    $category,
                    $thumbnailUrl,
                    $description,
                    $sources
                );

                logActivity(
                    "post_updated",
                    "Updated post: {$title}",
                    $currentUser["id"]
                );

                $successMessage = "Post updated successfully!";

                // Add image download confirmation messages
                if (!empty($downloadedImages)) {
                    foreach ($downloadedImages as $image) {
                        $successMessage .=
                            "<br>Downloaded: " .
                            htmlspecialchars($image["original_url"]) .
                            " → " .
                            htmlspecialchars($image["local_path"]);
                    }
                }

                addFlashMessage($successMessage, "success");
                redirect("posts.php?action=edit&id=" . $updatePostId);
                break;

            case "delete":
                $deletePostId = (int) $_POST["post_id"];
                $post = $postManager->getPost($deletePostId);

                if ($post) {
                    $postManager->deletePost($deletePostId);
                    logActivity(
                        "post_deleted",
                        "Deleted post: {$post["title"]}",
                        $currentUser["id"]
                    );
                    addFlashMessage("Post deleted successfully!", "success");
                } else {
                    addFlashMessage("Post not found.", "error");
                }

                redirect("posts.php");
                break;

            case "toggle_status":
                $togglePostId = (int) $_POST["post_id"];
                $post = $postManager->getPost($togglePostId);

                if ($post) {
                    $newStatus = $postManager->toggleDraftStatus($togglePostId);
                    $statusText = $newStatus ? "draft" : "published";

                    logActivity(
                        "post_status_changed",
                        "Changed post status to {$statusText}: {$post["title"]}",
                        $currentUser["id"]
                    );
                    addFlashMessage(
                        "Post status changed to {$statusText}!",
                        "success"
                    );
                } else {
                    addFlashMessage("Post not found.", "error");
                }

                redirect("posts.php");
                break;
        }
    } catch (Exception $e) {
        addFlashMessage($e->getMessage(), "error");
    }
}

// Handle different actions
switch ($action) {
    case "new":
        $pageTitle = "Create New Post";
        $post = null;
        break;

    case "edit":
        if ($postId <= 0) {
            addFlashMessage("Invalid post ID.", "error");
            redirect("posts.php");
        }

        try {
            $post = $postManager->getPost($postId);
            if (!$post) {
                addFlashMessage("Post not found.", "error");
                redirect("posts.php");
            }
            $pageTitle = "Edit Post: " . $post["title"];
        } catch (Exception $e) {
            addFlashMessage("Error loading post.", "error");
            redirect("posts.php");
        }
        break;

    default:
        $action = "list";
        $pageTitle = "Manage Posts";

        // Get posts with pagination
        $currentPage = getCurrentPage();
        $postsPerPage = 10;
        $status = $_GET["status"] ?? "";

        try {
            if ($status === "published") {
                $result = $postManager->getAllPosts(
                    $currentPage,
                    $postsPerPage,
                    true
                );
            } elseif ($status === "draft") {
                // Get only drafts
                $result = $postManager->getAllPosts(
                    $currentPage,
                    $postsPerPage,
                    false
                );
                $result["posts"] = array_filter($result["posts"], function (
                    $post
                ) {
                    return $post["is_draft"];
                });
            } else {
                $result = $postManager->getAllPosts(
                    $currentPage,
                    $postsPerPage,
                    false
                );
            }

            $posts = $result["posts"];
            $totalPages = $result["totalPages"];
            $total = $result["total"];
        } catch (Exception $e) {
            error_log("Error loading posts: " . $e->getMessage());
            $posts = [];
            $totalPages = 0;
            $total = 0;
            addFlashMessage("Error loading posts.", "error");
        }
        break;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo Security::escape($pageTitle); ?> - Admin</title>
    <link rel="stylesheet" href="/assets/style.css">
</head>
<body class="admin-page">
    <header class="admin-header">
        <div class="container">
            <h1 class="admin-title">
                <a href="index.php">Admin Dashboard</a>
            </h1>

            <nav class="admin-nav">
                <ul>
                    <li><a href="index.php">Dashboard</a></li>
                    <li><a href="posts.php" class="active">Posts</a></li>
                    <li><a href="media.php">Media</a></li>
                    <li><a href="profile.php">Profile</a></li>
                    <li><a href="api.php">API</a></li>
                    <li><a href="../index.php" target="_blank">View Site</a></li>
                    <li><a href="logout.php">Logout</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="admin-content">
        <div class="container">
            <?php echo displayFlashMessages(); ?>

            <div class="page-header">
                <h2><?php echo Security::escape($pageTitle); ?></h2>

                <?php if ($action === "list"): ?>
                    <div class="page-actions">
                        <a href="posts.php?action=new" class="button button-primary">
                            Create New Post
                        </a>
                    </div>
                <?php endif; ?>
            </div>

            <?php if ($action === "list"): ?>
                <!-- Posts List View -->
                <div class="posts-filters">
                    <div class="filter-tabs">
                        <a href="posts.php" class="filter-tab <?php echo $status ===
                        ""
                            ? "active"
                            : ""; ?>">
                            All Posts (<?php echo $total; ?>)
                        </a>
                        <a href="posts.php?status=published" class="filter-tab <?php echo $status ===
                        "published"
                            ? "active"
                            : ""; ?>">
                            Published
                        </a>
                        <a href="posts.php?status=draft" class="filter-tab <?php echo $status ===
                        "draft"
                            ? "active"
                            : ""; ?>">
                            Drafts
                        </a>
                    </div>
                </div>

                <?php if (empty($posts)): ?>
                    <div class="no-content">
                        <p>No posts found.</p>
                        <p><a href="posts.php?action=new">Create your first post</a></p>
                    </div>
                <?php else: ?>
                    <div class="posts-table">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Thumbnail</th>
                                    <th>Title</th>
                                    <th>Category</th>
                                    <th>Status</th>
                                    <th>Author</th>
                                    <th>Created</th>
                                    <th>Updated</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($posts as $post): ?>
                                    <tr>
                                        <td>
                                            <?php if (
                                                !empty($post["thumbnail_url"])
                                            ): ?>
                                                <img src="<?php echo Security::escape(
                                                    $post["thumbnail_url"]
                                                ); ?>"
                                                     alt="Thumbnail"
                                                     style="width: 60px; height: 40px; object-fit: cover; border-radius: 4px;">
                                            <?php else: ?>
                                                <div style="width: 60px; height: 40px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; display: flex; align-items: center; justify-content: center; font-size: 12px; color: #6c757d;">
                                                    No Image
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <strong><?php echo Security::escape(
                                                $post["title"]
                                            ); ?></strong>
                                            <div class="post-excerpt">
                                                <?php echo Security::escape(
                                                    truncateText(
                                                        strip_tags(
                                                            $post["content"]
                                                        ),
                                                        80
                                                    )
                                                ); ?>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="category-badge category-<?php echo Security::escape(
                                                $post["category"] ?? "tech"
                                            ); ?>">
                                                <?php echo Security::escape(
                                                    ucfirst(
                                                        $post["category"] ??
                                                            "tech"
                                                    )
                                                ); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="status-badge status-<?php echo $post[
                                                "is_draft"
                                            ]
                                                ? "draft"
                                                : "published"; ?>">
                                                <?php echo $post["is_draft"]
                                                    ? "Draft"
                                                    : "Published"; ?>
                                            </span>
                                        </td>
                                        <td><?php echo Security::escape(
                                            $post["author_name"]
                                        ); ?></td>
                                        <td><?php echo formatDate(
                                            $post["created_at"],
                                            "M j, Y"
                                        ); ?></td>
                                        <td><?php echo timeAgo(
                                            $post["updated_at"]
                                        ); ?></td>
                                        <td class="actions">
                                            <a href="posts.php?action=edit&id=<?php echo $post[
                                                "id"
                                            ]; ?>"
                                               class="action-link">Edit</a>

                                            <?php if (!$post["is_draft"]): ?>
                                                <a href="<?php echo ".." .
                                                    generatePostUrl($post); ?>"
                                                   class="action-link" target="_blank">View</a>
                                            <?php endif; ?>

                                            <form method="POST" style="display: inline;"
                                                  onsubmit="return confirm('Toggle post status?')">
                                                <?php echo csrfTokenField(); ?>
                                                <input type="hidden" name="action" value="toggle_status">
                                                <input type="hidden" name="post_id" value="<?php echo $post[
                                                    "id"
                                                ]; ?>">
                                                <button type="submit" class="action-link">
                                                    <?php echo $post["is_draft"]
                                                        ? "Publish"
                                                        : "Unpublish"; ?>
                                                </button>
                                            </form>

                                            <form method="POST" style="display: inline;"
                                                  onsubmit="return confirm('Are you sure you want to delete this post?')">
                                                <?php echo csrfTokenField(); ?>
                                                <input type="hidden" name="action" value="delete">
                                                <input type="hidden" name="post_id" value="<?php echo $post[
                                                    "id"
                                                ]; ?>">
                                                <button type="submit" class="action-link danger">Delete</button>
                                            </form>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <div class="pagination-container">
                            <?php
                            $baseUrl =
                                "posts.php" .
                                ($status ? "?status=" . $status : "");
                            echo generatePagination(
                                $currentPage,
                                $totalPages,
                                $baseUrl
                            );
                            ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>

            <?php else: ?>
                <!-- Post Form View -->
                <div class="post-form-container">
                    <form method="POST" action="posts.php" class="post-form" enctype="multipart/form-data">
                        <?php echo csrfTokenField(); ?>
                        <input type="hidden" name="action" value="<?php echo $post
                            ? "update"
                            : "create"; ?>">
                        <?php if ($post): ?>
                            <input type="hidden" name="post_id" value="<?php echo $post[
                                "id"
                            ]; ?>">
                        <?php endif; ?>

                        <div class="form-group">
                            <label for="title">Title *</label>
                            <input type="text"
                                   id="title"
                                   name="title"
                                   value="<?php echo Security::escape(
                                       $post["title"] ?? ""
                                   ); ?>"
                                   required
                                   maxlength="255"
                                   class="form-control">
                        </div>

                        <div class="form-group">
                            <label for="description">Description *</label>
                            <input type="text"
                                   id="description"
                                   name="description"
                                   value="<?php echo Security::escape(
                                       $post["description"] ?? ""
                                   ); ?>"
                                   required
                                   maxlength="500"
                                   placeholder="Brief description for SEO and social media previews (max 500 characters)"
                                   class="form-control">
                            <div class="form-help">
                                This description will be used for SEO meta tags, social media previews (Facebook, Twitter), and post previews on the main page.
                                <span id="description-counter" class="char-counter">0/500 characters</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>Thumbnail Image *</label>

                            <!-- Tab Navigation -->
                            <div class="thumbnail-tabs">
                                <button type="button" class="tab-btn active" data-tab="upload">Upload & Crop</button>
                                <button type="button" class="tab-btn" data-tab="url">Use URL</button>
                            </div>

                            <!-- Upload Tab -->
                            <div class="tab-content" id="upload-tab">
                                <div class="image-upload-area">
                                    <input type="file"
                                           id="thumbnail_image"
                                           name="thumbnail_image"
                                           accept="image/*"
                                           class="form-control">
                                    <div class="upload-help">
                                        Upload an image file (JPEG, PNG, GIF, WebP). Maximum size: 5MB.
                                    </div>

                                    <!-- Image Preview and Cropping Area -->
                                    <div id="image-preview" style="display: none; margin-top: 15px;">
                                        <div class="image-crop-container">
                                            <img id="crop-image" alt="Image to crop">
                                        </div>
                                        <div class="crop-controls">
                                            <button type="button" id="crop-btn" class="button button-primary">Apply Crop</button>
                                            <button type="button" id="reset-crop" class="button">Reset</button>
                                            <button type="button" id="zoom-in" class="button">Zoom In</button>
                                            <button type="button" id="zoom-out" class="button">Zoom Out</button>
                                            <span id="crop-info" style="margin-left: 15px; font-size: 14px; color: #666;"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- URL Tab -->
                            <div class="tab-content" id="url-tab" style="display: none;">
                                <input type="url"
                                       id="thumbnail_url"
                                       name="thumbnail_url"
                                       value="<?php echo Security::escape(
                                           $post["thumbnail_url"] ?? ""
                                       ); ?>"
                                       placeholder="https://example.com/image.jpg"
                                       class="form-control">
                                <div class="form-help">
                                    Enter a URL for the post thumbnail image. Leave empty for no thumbnail.
                                </div>
                            </div>

                            <!-- Current Thumbnail Preview -->
                            <?php if (!empty($post["thumbnail_url"])): ?>
                                <div class="current-thumbnail" style="margin-top: 15px;">
                                    <label>Current Thumbnail:</label>
                                    <div style="margin-top: 5px;">
                                        <img src="<?php echo Security::escape(
                                            $post["thumbnail_url"]
                                        ); ?>"
                                             alt="Current thumbnail"
                                             style="max-width: 200px; height: auto; border: 1px solid #dee2e6; border-radius: 4px;">
                                    </div>
                                    <?php if (
                                        strpos(
                                            $post["thumbnail_url"],
                                            "/uploads/"
                                        ) === 0
                                    ): ?>
                                        <button type="button" id="delete-current-image" class="button" style="margin-top: 5px;">
                                            Delete Current Image
                                        </button>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>

                            <!-- Hidden field for crop data -->
                            <input type="hidden" id="crop_data" name="crop_data">
                        </div>

                        <div class="form-group">
                            <label for="content">Content * <span class="markdown-hint">(Markdown supported)</span></label>
                            <div class="content-editor-container">
                                <div class="content-editor-tabs">
                                    <button type="button" class="editor-tab active" data-tab="editor">Editor</button>
                                    <button type="button" class="editor-tab" data-tab="preview">Preview</button>
                                    <button type="button" class="editor-tab" data-tab="split">Split View</button>
                                </div>

                                <div class="content-editor-wrapper">
                                    <div class="content-editor-pane" id="editor-pane">
                                        <textarea id="content"
                                                  name="content"
                                                  required
                                                  rows="20"
                                                  placeholder="Write your content here using Markdown syntax..."
                                                  class="form-control content-textarea"><?php echo $post[
                                                      "content"
                                                  ] ?? ""; ?></textarea>
                                    </div>

                                    <div class="content-preview-pane" id="preview-pane" style="display: none;">
                                        <div class="preview-content" id="preview-content">
                                            <div class="preview-placeholder">
                                                <p>Start typing to see your content preview...</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="markdown-toolbar">
                                    <button type="button" class="toolbar-btn" data-action="bold" title="Bold">
                                        <strong>B</strong>
                                    </button>
                                    <button type="button" class="toolbar-btn" data-action="italic" title="Italic">
                                        <em>I</em>
                                    </button>
                                    <button type="button" class="toolbar-btn" data-action="heading" title="Heading">
                                        H1
                                    </button>
                                    <button type="button" class="toolbar-btn" data-action="link" title="Link">
                                        🔗
                                    </button>
                                    <button type="button" class="toolbar-btn" data-action="image" title="Image">
                                        🖼
                                    </button>
                                    <button type="button" class="toolbar-btn" data-action="list" title="List">
                                        📝
                                    </button>
                                    <button type="button" class="toolbar-btn" data-action="code" title="Code">
                                        &lt;/&gt;
                                    </button>
                                    <button type="button" class="toolbar-btn" data-action="quote" title="Quote">
                                        ❝
                                    </button>
                                    <div class="toolbar-help">
                                        <a href="https://www.markdownguide.org/basic-syntax/" target="_blank" rel="noopener">
                                            📖 Markdown Guide
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="sources">Sources (comma-separated URLs)</label>
                            <input type="text"
                                   id="sources"
                                   name="sources"
                                   class="form-control"
                                   placeholder="https://example.com, https://another-source.com"
                                   value="<?php echo Security::escape(
                                       $post["sources"] ?? ""
                                   ); ?>">
                            <small class="form-help">
                                Enter source URLs separated by commas. These will be displayed as clickable links below your article.
                            </small>
                        </div>

                        <div class="form-group">
                            <label for="category">Category *</label>
                            <select id="category"
                                    name="category"
                                    required
                                    class="form-control">
                                <?php
                                $validCategories = Post::getValidCategories();
                                $selectedCategory = $post["category"] ?? "tech";
                                foreach ($validCategories as $category): ?>
                                    <option value="<?php echo Security::escape(
                                        $category
                                    ); ?>"
                                            <?php echo $selectedCategory ===
                                            $category
                                                ? "selected"
                                                : ""; ?>>
                                        <?php echo Security::escape(
                                            ucfirst($category)
                                        ); ?>
                                    </option>
                                <?php endforeach;
                                ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox"
                                       name="is_draft"
                                       value="1"
                                       <?php echo $post && $post["is_draft"]
                                           ? "checked"
                                           : ""; ?>>
                                Save as draft
                            </label>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="button button-primary">
                                <?php echo $post
                                    ? "Update Post"
                                    : "Create Post"; ?>
                            </button>
                            <a href="posts.php" class="button">Cancel</a>

                            <?php if ($post && !$post["is_draft"]): ?>
                                <a href="<?php echo ".." .
                                    generatePostUrl($post); ?>"
                                   class="button" target="_blank">View Post</a>
                            <?php endif; ?>
                        </div>
                    </form>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <!-- Include Cropper.js -->
    <link rel="stylesheet" href="https://unpkg.com/cropperjs@1.6.1/dist/cropper.css">
    <script src="https://unpkg.com/cropperjs@1.6.1/dist/cropper.min.js"></script>

    <!-- Include Marked.js for Markdown parsing -->
    <script src="https://unpkg.com/marked@9.1.2/marked.min.js"></script>

    <script>
        // Debug: Check if Cropper.js is loaded
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof Cropper === 'undefined') {
                console.error('Cropper.js is not loaded!');
                alert('Cropper.js library failed to load. Please check your internet connection.');
            } else {
                console.log('Cropper.js loaded successfully');
            }
        });
    </script>

    <script>
        let cropper = null;
        let currentImageFile = null;

        // Tab switching functionality
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const targetTab = this.dataset.tab;

                // Update active tab button
                document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');

                // Show/hide tab content
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.style.display = 'none';
                });
                document.getElementById(targetTab + '-tab').style.display = 'block';

                // Clear the other input when switching tabs
                if (targetTab === 'upload') {
                    document.getElementById('thumbnail_url').value = '';
                } else {
                    document.getElementById('thumbnail_image').value = '';
                    if (cropper) {
                        cropper.destroy();
                        cropper = null;
                    }
                    document.getElementById('image-preview').style.display = 'none';
                }
            });
        });

        // File input change handler
        document.getElementById('thumbnail_image').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (!file) return;

            // Validate file type
            if (!file.type.match(/^image\/(jpeg|png|gif|webp)$/)) {
                alert('Please select a valid image file (JPEG, PNG, GIF, or WebP).');
                e.target.value = '';
                return;
            }

            // Validate file size (5MB)
            if (file.size > 5 * 1024 * 1024) {
                alert('File size must be less than 5MB.');
                e.target.value = '';
                return;
            }

            currentImageFile = file;

            // Create preview
            const reader = new FileReader();
            reader.onload = function(e) {
                const cropImage = document.getElementById('crop-image');
                cropImage.src = e.target.result;
                document.getElementById('image-preview').style.display = 'block';

                // Destroy existing cropper
                if (cropper) {
                    cropper.destroy();
                    cropper = null;
                }

                // Wait for image to actually load in DOM
                cropImage.onload = function() {
                    console.log('Image loaded, dimensions:', this.naturalWidth, 'x', this.naturalHeight);

                    // Small delay to ensure DOM is ready
                    setTimeout(() => {
                        if (typeof Cropper === 'undefined') {
                            console.error('Cropper class not available');
                            alert('Cropper.js library is not loaded. Please refresh the page.');
                            return;
                        }

                        try {
                            console.log('Initializing cropper...');
                            cropper = new Cropper(cropImage, {
                                aspectRatio: 16 / 9,
                                viewMode: 1,
                                dragMode: 'crop',
                                responsive: true,
                                autoCropArea: 0.8,
                                background: false,
                                guides: true,
                                center: true,
                                highlight: false,
                                cropBoxMovable: true,
                                cropBoxResizable: true,
                                toggleDragModeOnDblclick: false,
                                ready: function() {
                                    console.log('Cropper is ready!');
                                    console.log('Container size:', this.cropper.containerData);
                                    updateCropInfo();
                                },
                                crop: function(event) {
                                    updateCropInfo();
                                }
                            });
                        } catch (error) {
                            console.error('Error initializing cropper:', error);
                            alert('Failed to initialize image cropper: ' + error.message);
                        }
                    }, 100);
                };
            };
            reader.readAsDataURL(file);
        });

        // Update crop info display
        function updateCropInfo() {
            if (!cropper) return;

            const cropData = cropper.getData();
            const info = `Size: ${Math.round(cropData.width)} × ${Math.round(cropData.height)}px`;
            document.getElementById('crop-info').textContent = info;
        }

        // Apply crop button
        document.getElementById('crop-btn').addEventListener('click', function() {
            if (!cropper) return;

            const cropData = cropper.getData();
            document.getElementById('crop_data').value = JSON.stringify({
                x: Math.round(cropData.x),
                y: Math.round(cropData.y),
                width: Math.round(cropData.width),
                height: Math.round(cropData.height)
            });

            alert('Crop area selected! The image will be cropped when you save the post.');
        });

        // Reset crop button
        document.getElementById('reset-crop').addEventListener('click', function() {
            if (cropper) {
                cropper.reset();
                document.getElementById('crop_data').value = '';
                updateCropInfo();
            }
        });

        // Zoom controls
        document.getElementById('zoom-in').addEventListener('click', function() {
            if (cropper) {
                cropper.zoom(0.1);
            }
        });

        document.getElementById('zoom-out').addEventListener('click', function() {
            if (cropper) {
                cropper.zoom(-0.1);
            }
        });

        // Delete current image button
        const deleteBtn = document.getElementById('delete-current-image');
        if (deleteBtn) {
            deleteBtn.addEventListener('click', function() {
                if (confirm('Are you sure you want to delete the current thumbnail image?')) {
                    document.getElementById('thumbnail_url').value = '';
                    this.parentElement.style.display = 'none';
                }
            });
        }

        // Character counter for description
        const descriptionField = document.getElementById('description');
        const descriptionCounter = document.getElementById('description-counter');

        function updateDescriptionCounter() {
            const length = descriptionField.value.length;
            const maxLength = 500;
            descriptionCounter.textContent = `${length}/${maxLength} characters`;

            if (length > maxLength * 0.9) {
                descriptionCounter.style.color = '#dc3545'; // Red when approaching limit
            } else if (length > maxLength * 0.7) {
                descriptionCounter.style.color = '#fd7e14'; // Orange when getting close
            } else {
                descriptionCounter.style.color = '#6c757d'; // Default gray
            }
        }

        // Update counter on page load
        updateDescriptionCounter();

        // Update counter as user types
        descriptionField.addEventListener('input', updateDescriptionCounter);
        descriptionField.addEventListener('keyup', updateDescriptionCounter);

        // Markdown Editor functionality
        let currentView = 'editor';
        let previewUpdateTimeout;

        // Initialize markdown editor
        function initMarkdownEditor() {
            const contentTextarea = document.getElementById('content');
            const editorTabs = document.querySelectorAll('.editor-tab');
            const editorWrapper = document.querySelector('.content-editor-wrapper');
            const editorPane = document.getElementById('editor-pane');
            const previewPane = document.getElementById('preview-pane');
            const previewContent = document.getElementById('preview-content');

            // Tab switching
            editorTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const targetView = this.dataset.tab;
                    switchView(targetView);
                });
            });

            function switchView(view) {
                currentView = view;

                // Update active tab
                editorTabs.forEach(tab => {
                    tab.classList.toggle('active', tab.dataset.tab === view);
                });

                // Update view
                switch (view) {
                    case 'editor':
                        editorWrapper.classList.remove('split-view');
                        editorPane.style.display = 'block';
                        previewPane.style.display = 'none';
                        break;
                    case 'preview':
                        editorWrapper.classList.remove('split-view');
                        editorPane.style.display = 'none';
                        previewPane.style.display = 'block';
                        updatePreview();
                        break;
                    case 'split':
                        editorWrapper.classList.add('split-view');
                        editorPane.style.display = 'block';
                        previewPane.style.display = 'block';

                        updatePreview();
                        break;
                }
            }

            // Update preview content
            function updatePreview() {
                const content = contentTextarea.value.trim();

                if (!content) {
                    previewContent.innerHTML = '<div class="preview-placeholder"><p>Start typing to see your content preview...</p></div>';
                    return;
                }

                try {
                    const html = marked.parse(content);
                    previewContent.innerHTML = html;
                } catch (error) {
                    console.error('Markdown parsing error:', error);
                    previewContent.innerHTML = '<div class="preview-placeholder"><p>Error parsing markdown content.</p></div>';
                }
            }

            // Auto-update preview on content change
            contentTextarea.addEventListener('input', function() {
                if (currentView === 'preview' || currentView === 'split') {
                    clearTimeout(previewUpdateTimeout);
                    previewUpdateTimeout = setTimeout(updatePreview, 300);
                }
            });

            // Toolbar actions
            const toolbarButtons = document.querySelectorAll('.toolbar-btn[data-action]');
            toolbarButtons.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const action = this.dataset.action;
                    insertMarkdown(action);
                });
            });

            function insertMarkdown(action) {
                const textarea = contentTextarea;
                const start = textarea.selectionStart;
                const end = textarea.selectionEnd;
                const selectedText = textarea.value.substring(start, end);
                let replacement = '';
                let cursorOffset = 0;

                switch (action) {
                    case 'bold':
                        replacement = `**${selectedText || 'bold text'}**`;
                        cursorOffset = selectedText ? 0 : -11;
                        break;
                    case 'italic':
                        replacement = `*${selectedText || 'italic text'}*`;
                        cursorOffset = selectedText ? 0 : -12;
                        break;
                    case 'heading':
                        replacement = `# ${selectedText || 'Heading'}`;
                        cursorOffset = selectedText ? 0 : -7;
                        break;
                    case 'link':
                        replacement = `[${selectedText || 'link text'}](url)`;
                        cursorOffset = selectedText ? -5 : -15;
                        break;
                    case 'image':
                        replacement = `![${selectedText || 'alt text'}](image-url)`;
                        cursorOffset = selectedText ? -12 : -21;
                        break;
                    case 'list':
                        replacement = `- ${selectedText || 'list item'}`;
                        cursorOffset = selectedText ? 0 : -9;
                        break;
                    case 'code':
                        if (selectedText.includes('\n')) {
                            replacement = `\`\`\`\n${selectedText || 'code'}\n\`\`\``;
                            cursorOffset = selectedText ? 0 : -8;
                        } else {
                            replacement = `\`${selectedText || 'code'}\``;
                            cursorOffset = selectedText ? 0 : -6;
                        }
                        break;
                    case 'quote':
                        replacement = `> ${selectedText || 'quote'}`;
                        cursorOffset = selectedText ? 0 : -5;
                        break;
                }

                // Insert the replacement text
                textarea.value = textarea.value.substring(0, start) + replacement + textarea.value.substring(end);

                // Update cursor position
                const newPosition = start + replacement.length + cursorOffset;
                textarea.setSelectionRange(newPosition, newPosition);
                textarea.focus();

                // Update preview if needed
                if (currentView === 'preview' || currentView === 'split') {
                    clearTimeout(previewUpdateTimeout);
                    previewUpdateTimeout = setTimeout(updatePreview, 100);
                }
            }

            // Initial preview update if content exists
            if (contentTextarea.value.trim()) {
                updatePreview();
            }
        }

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof marked !== 'undefined') {
                // Configure marked options
                marked.setOptions({
                    breaks: true,
                    gfm: true,
                    sanitize: false,
                    smartypants: true
                });

                initMarkdownEditor();
            } else {
                console.error('Marked.js not loaded');
            }
        });

        // Form validation
        document.querySelector('.post-form').addEventListener('submit', function(e) {
            const uploadTab = document.getElementById('upload-tab');
            const urlTab = document.getElementById('url-tab');
            const uploadInput = document.getElementById('thumbnail_image');
            const urlInput = document.getElementById('thumbnail_url');
            const descriptionInput = document.getElementById('description');

            // Validate description is required
            if (!descriptionInput.value.trim()) {
                e.preventDefault();
                alert('Description is required.');
                descriptionInput.focus();
                return false;
            }

            // Validate thumbnail is required
            const hasUploadedFile = uploadInput.files.length > 0;
            const hasUrlValue = urlInput.value.trim();

            if (!hasUploadedFile && !hasUrlValue) {
                e.preventDefault();
                alert('Thumbnail is required. Please upload an image or provide a URL.');
                return false;
            }

            // Check if upload tab is active and file is selected
            if (uploadTab.style.display !== 'none' && uploadInput.files.length > 0) {
                // Clear URL input to avoid conflicts
                urlInput.value = '';
            }
        });
    </script>

    <style>
        .thumbnail-tabs {
            margin-bottom: 15px;
            border-bottom: 1px solid #dee2e6;
        }

        .tab-btn {
            padding: 10px 20px;
            border: none;
            background: transparent;
            border-bottom: 2px solid transparent;
            cursor: pointer;
            margin-right: 10px;
            font-size: 14px;
        }

        .tab-btn.active {
            border-bottom-color: #007bff;
            color: #007bff;
            font-weight: 500;
        }

        .tab-btn:hover {
            background: #f8f9fa;
        }

        .image-upload-area {
            padding: 20px;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            text-align: center;
        }

        .upload-help {
            margin-top: 10px;
            font-size: 14px;
            color: #6c757d;
        }

        .image-crop-container {
            width: 100%;
            max-width: 600px;
            height: 400px;
            margin: 20px auto;
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            position: relative;
        }

        .image-crop-container img {
            max-width: 100%;
            max-height: 100%;
            display: block;
        }

        .crop-controls {
            text-align: center;
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
        }

        .crop-controls .button {
            margin: 0 5px;
        }

        .current-thumbnail label {
            font-weight: 500;
            color: #495057;
        }

        .char-counter {
            display: block;
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
            text-align: right;
        }

        /* Markdown Editor Styles */
        .markdown-hint {
            font-size: 12px;
            color: #007bff;
            font-weight: normal;
        }

        .content-editor-container {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
            background: #fff;
        }

        .content-editor-tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .editor-tab {
            padding: 10px 20px;
            background: transparent;
            border: none;
            cursor: pointer;
            font-size: 14px;
            color: #6c757d;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
        }

        .editor-tab.active {
            color: #007bff;
            border-bottom-color: #007bff;
            font-weight: 500;
        }

        .editor-tab:hover {
            background: #e9ecef;
        }

        .content-editor-wrapper {
            position: relative;
            min-height: 400px;
        }

        .content-editor-pane {
            width: 100%;
            height: 100%;
        }

        .content-textarea {
            border: none;
            resize: vertical;
            min-height: 400px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.5;
            padding: 20px;
        }

        .content-textarea:focus {
            outline: none;
            box-shadow: none;
        }

        .content-preview-pane {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow-y: auto;
            background: #fff;
            padding: 20px;
        }

        .preview-content {
            max-width: none;
            line-height: 1.6;
        }

        .preview-content h1,
        .preview-content h2,
        .preview-content h3,
        .preview-content h4,
        .preview-content h5,
        .preview-content h6 {
            margin-top: 1.5em;
            margin-bottom: 0.5em;
            font-weight: 600;
        }

        .preview-content h1 { font-size: 2em; }
        .preview-content h2 { font-size: 1.5em; }
        .preview-content h3 { font-size: 1.25em; }

        .preview-content p {
            margin-bottom: 1em;
        }

        .preview-content ul,
        .preview-content ol {
            margin-bottom: 1em;
            padding-left: 2em;
        }

        .preview-content li {
            margin-bottom: 0.25em;
        }

        .preview-content blockquote {
            border-left: 4px solid #007bff;
            padding-left: 1em;
            margin: 1em 0;
            color: #6c757d;
            font-style: italic;
        }

        .preview-content code {
            background: #f8f9fa;
            padding: 0.2em 0.4em;
            border-radius: 3px;
            font-size: 0.9em;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }

        .preview-content pre {
            background: #f8f9fa;
            padding: 1em;
            border-radius: 6px;
            overflow-x: auto;
            margin: 1em 0;
        }

        .preview-content pre code {
            background: none;
            padding: 0;
        }

        .preview-content table {
            border-collapse: collapse;
            width: 100%;
            margin: 1em 0;
        }

        .preview-content table th,
        .preview-content table td {
            border: 1px solid #dee2e6;
            padding: 0.5em;
            text-align: left;
        }

        .preview-content table th {
            background: #f8f9fa;
            font-weight: 600;
        }

        .preview-content img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
        }

        .preview-content a {
            color: #007bff;
            text-decoration: none;
        }

        .preview-content a:hover {
            text-decoration: underline;
        }

        .preview-placeholder {
            text-align: center;
            color: #6c757d;
            font-style: italic;
            margin-top: 2em;
        }

        .markdown-toolbar {
            display: flex;
            align-items: center;
            gap: 5px;
            padding: 10px 15px;
            background: #f8f9fa;
            border-top: 1px solid #dee2e6;
            flex-wrap: wrap;
        }

        .toolbar-btn {
            padding: 5px 10px;
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
        }

        .toolbar-btn:hover {
            background: #e9ecef;
            border-color: #adb5bd;
        }

        .toolbar-help {
            margin-left: auto;
            font-size: 12px;
        }

        .toolbar-help a {
            color: #007bff;
            text-decoration: none;
        }

        .toolbar-help a:hover {
            text-decoration: underline;
        }

        /* Split view styles */
        .content-editor-container .content-editor-wrapper.split-view {
            display: flex !important;
            height: 500px;
        }

        .content-editor-container .content-editor-wrapper.split-view .content-editor-pane {
            width: 50%;
            height: 100%;
            border-right: 1px solid #dee2e6;
        }

        .content-editor-container .content-editor-wrapper.split-view .content-preview-pane {
            position: static !important;
            width: 50% !important;
            height: 100%;
            display: block !important;
            top: auto !important;
            left: auto !important;
        }

        .content-editor-container .content-editor-wrapper.split-view .content-textarea {
            min-height: 100%;
            height: 100%;
            border-right: 1px solid #dee2e6;
        }
    </style>
</body>
</html>
