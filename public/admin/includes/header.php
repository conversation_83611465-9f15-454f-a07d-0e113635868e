<?php
/**
 * Shared Admin Header Component
 * Provides consistent header and navigation across all admin pages
 */

// Prevent direct access
if (!defined('CMS_INIT')) {
    die('Direct access not allowed');
}

/**
 * Get the current page name for navigation highlighting
 */
function getCurrentPageName() {
    $currentScript = basename($_SERVER['SCRIPT_NAME']);
    return $currentScript;
}

/**
 * Check if a navigation item should be marked as active
 */
function isNavActive($page) {
    $currentPage = getCurrentPageName();
    return $currentPage === $page ? 'active' : '';
}

/**
 * Render admin navigation with proper active states
 */
function renderAdminNavigation() {
    echo '[renderAdminNavigation] start' . PHP_EOL;
    
    $navItems = [
        'index.php' => 'Dashboard',
        'posts.php' => 'Posts', 
        'media.php' => 'Media',
        'profile.php' => 'Profile',
        'api.php' => 'API',
        'scraper.php' => 'Scraper'
    ];
    
    echo '<nav class="admin-nav">' . PHP_EOL;
    echo '    <ul>' . PHP_EOL;
    
    foreach ($navItems as $page => $label) {
        $activeClass = isNavActive($page);
        $classAttr = $activeClass ? ' class="' . $activeClass . '"' : '';
        echo '        <li><a href="' . $page . '"' . $classAttr . '>' . $label . '</a></li>' . PHP_EOL;
    }
    
    // Special navigation items
    echo '        <li><a href="../index.php" target="_blank">View Site</a></li>' . PHP_EOL;
    echo '        <li><a href="logout.php">Logout</a></li>' . PHP_EOL;
    echo '    </ul>' . PHP_EOL;
    echo '</nav>' . PHP_EOL;
    
    echo '[renderAdminNavigation] end' . PHP_EOL;
}

/**
 * Render the complete admin header
 */
function renderAdminHeader($pageTitle = 'Admin Dashboard') {
    echo '[renderAdminHeader] start' . PHP_EOL;
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title><?php echo Security::escape($pageTitle); ?> - Minimal CMS</title>
        <link rel="stylesheet" href="/assets/style.css">
    </head>
    <body class="admin-page">
        <header class="admin-header">
            <div class="container">
                <h1 class="admin-title">
                    <a href="index.php">Admin Dashboard</a>
                </h1>
                
                <?php renderAdminNavigation(); ?>
            </div>
        </header>
        
        <main class="admin-content">
            <div class="container">
                <?php echo displayFlashMessages(); ?>
    <?php
    echo '[renderAdminHeader] end' . PHP_EOL;
}
?>
