<?php
/**
 * API Documentation Page
 * Shows REST API usage and endpoint documentation
 */

if (!defined("CMS_INIT")) {
    define("CMS_INIT", true);
    require_once "../../src/includes/init.php";
}

// Require authentication
$auth->requireAuth();

$currentUser = $auth->getCurrentUser();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Documentation - Admin</title>
    <link rel="stylesheet" href="/assets/style.css">
    <style>
        .api-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .endpoint {
            background: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .method {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            color: white;
            font-weight: bold;
            font-size: 0.875rem;
        }
        .method.post { background-color: #28a745; }
        .method.get { background-color: #007bff; }
        .method.put { background-color: #ffc107; color: #212529; }
        .method.delete { background-color: #dc3545; }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 1rem;
            margin: 0.5rem 0;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            overflow-x: auto;
        }
        .response-example {
            background: #e8f5e8;
            border-left: 4px solid #28a745;
        }
        .error-example {
            background: #f8d7da;
            border-left: 4px solid #dc3545;
        }
        .token-highlight {
            background-color: #fff3cd;
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            font-family: monospace;
            border: 1px solid #ffeaa7;
        }
        .quick-test {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 5px;
            padding: 1rem;
            margin-top: 1rem;
        }
    </style>
</head>
<body class="admin-page">
    <header class="admin-header">
        <div class="container">
            <h1 class="admin-title">
                <a href="index.php">Admin Dashboard</a>
            </h1>

            <nav class="admin-nav">
                <ul>
                    <li><a href="index.php">Dashboard</a></li>
                    <li><a href="posts.php">Posts</a></li>
                    <li><a href="media.php">Media</a></li>
                    <li><a href="profile.php">Profile</a></li>
                    <li><a href="api.php" class="active">API</a></li>
                    <li><a href="../index.php" target="_blank">View Site</a></li>
                    <li><a href="logout.php">Logout</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="admin-content">
        <div class="container">
            <div class="page-header">
                <h2>REST API Documentation</h2>
                <p>Use these endpoints to interact with your CMS from external applications.</p>
            </div>

            <!-- Authentication Section -->
            <div class="api-section">
                <h3>🔐 Authentication</h3>
                <p>All API endpoints require authentication using a Bearer token. Only admin users have access to API tokens.</p>

                <h4>Your API Token:</h4>
                <div class="token-highlight">
                    <?php echo Security::escape(
                        $currentUser["api_token"] ?? "No token available"
                    ); ?>
                </div>

                <p><strong>Note:</strong> You can manage your API token in your <a href="profile.php">Profile settings</a>.</p>

                <h4>How to authenticate:</h4>
                <div class="code-block">
Authorization: Bearer <?php echo Security::escape(
    $currentUser["api_token"] ?? "YOUR_TOKEN"
); ?>
Content-Type: application/json
                </div>
            </div>

            <!-- Base URL Section -->
            <div class="api-section">
                <h3>🌐 Base URL</h3>
                <div class="code-block">
<?php
$protocol =
    isset($_SERVER["HTTPS"]) && $_SERVER["HTTPS"] === "on" ? "https" : "http";
$host = $_SERVER["HTTP_HOST"];
$baseUrl = $protocol . "://" . $host . "/api/";
echo $baseUrl;
?>
                </div>
            </div>

            <!-- Endpoints Section -->
            <div class="api-section">
                <h3>📋 Available Endpoints</h3>

                <!-- Create Post Endpoint -->
                <div class="endpoint">
                    <h4>
                        <span class="method post">POST</span>
                        /api/posts.php
                    </h4>
                    <p><strong>Description:</strong> Create a new blog post</p>

                    <h5>Request Headers:</h5>
                    <div class="code-block">
Authorization: Bearer <?php echo Security::escape(
    $currentUser["api_token"] ?? "YOUR_TOKEN"
); ?>
Content-Type: application/json
                    </div>

                    <h5>Request Body:</h5>
                    <div class="code-block">
{
    "title": "Your Post Title",
    "content": "Your post content here. Supports **markdown** formatting.",
    "description": "Brief description for SEO and social media (max 500 chars)",
    "thumbnail_url": "https://example.com/image.jpg"
}
                    </div>

                    <h5>Success Response (201 Created):</h5>
                    <div class="code-block response-example">
{
    "success": true,
    "id": 123,
    "message": "Post created successfully"
}
                    </div>

                    <h5>Error Responses:</h5>
                    <div class="code-block error-example">
// 401 Unauthorized - Invalid or missing token
{
    "error": "Invalid token"
}

// 400 Bad Request - Missing required fields
{
    "error": "Missing required fields: title, content, description, and thumbnail_url are required"
}

// 500 Internal Server Error - Database error
{
    "error": "Failed to create post"
}
                    </div>
                </div>
            </div>

            <!-- Usage Examples -->
            <div class="api-section">
                <h3>💡 Usage Examples</h3>

                <h4>cURL Example:</h4>
                <div class="code-block">
curl -X POST <?php echo $baseUrl; ?>posts.php \
  -H "Authorization: Bearer <?php echo Security::escape(
      $currentUser["api_token"] ?? "YOUR_TOKEN"
  ); ?>" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "My API Post",
    "content": "This post was created via the REST API!",
    "description": "A sample post created through the REST API",
    "thumbnail_url": "https://example.com/thumbnail.jpg"
  }'
                </div>

                <h4>JavaScript (Fetch API) Example:</h4>
                <div class="code-block">
fetch('<?php echo $baseUrl; ?>posts.php', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer <?php echo Security::escape(
            $currentUser["api_token"] ?? "YOUR_TOKEN"
        ); ?>',
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        title: 'My API Post',
        content: 'This post was created via the REST API!',
        description: 'A sample post created through the REST API',
        thumbnail_url: 'https://example.com/thumbnail.jpg'
    })
})
.then(response => response.json())
.then(data => console.log('Success:', data))
.catch(error => console.error('Error:', error));
                </div>

                <h4>Python (requests) Example:</h4>
                <div class="code-block">
import requests

url = '<?php echo $baseUrl; ?>posts.php'
headers = {
    'Authorization': 'Bearer <?php echo Security::escape(
        $currentUser["api_token"] ?? "YOUR_TOKEN"
    ); ?>',
    'Content-Type': 'application/json'
}
data = {
    'title': 'My API Post',
    'content': 'This post was created via the REST API!',
    'description': 'A sample post created through the REST API',
    'thumbnail_url': 'https://example.com/thumbnail.jpg'
}

response = requests.post(url, headers=headers, json=data)
print(response.json())
                </div>
            </div>

            <!-- Security Best Practices -->
            <div class="api-section">
                <h3>🔒 Security Best Practices</h3>
                <ul>
                    <li><strong>Keep your API token secure:</strong> Never share your token or commit it to version control</li>
                    <li><strong>Use HTTPS:</strong> Always use HTTPS in production to encrypt token transmission</li>
                    <li><strong>Regenerate tokens:</strong> Regenerate your token if you suspect it has been compromised</li>
                    <li><strong>Validate responses:</strong> Always check the response status and handle errors appropriately</li>
                    <li><strong>Rate limiting:</strong> Be mindful of request frequency to avoid overwhelming the server</li>
                </ul>
            </div>

            <!-- Quick Test Section -->
            <div class="quick-test">
                <h3>🧪 Quick Test</h3>
                <p>Want to test the API quickly? Use our <a href="../test.html" target="_blank">API Test Interface</a>.</p>
                <p>Your token is already configured in your profile, so you can copy it directly from there.</p>
            </div>

            <!-- Rate Limits and Future Features -->
            <div class="api-section">
                <h3>📊 Limitations & Future Features</h3>
                <h4>Current Limitations:</h4>
                <ul>
                    <li>Only POST (create) operations are currently supported</li>
                    <li>Only admin users can access the API</li>
                    <li>No rate limiting (planned for future release)</li>
                </ul>

                <h4>Coming Soon:</h4>
                <ul>
                    <li>GET endpoints to retrieve posts</li>
                    <li>PUT endpoints to update existing posts</li>
                    <li>DELETE endpoints to remove posts</li>
                    <li>Rate limiting and usage analytics</li>
                    <li>Webhook support</li>
                </ul>
            </div>

            <!-- Support Section -->
            <div class="api-section">
                <h3>📞 Support</h3>
                <p>If you encounter any issues with the API:</p>
                <ul>
                    <li>Check the server logs for detailed error messages</li>
                    <li>Ensure your token is valid and hasn't expired</li>
                    <li>Verify that your request format matches the examples above</li>
                    <li>Make sure you're using the correct HTTP method and headers</li>
                </ul>
            </div>
        </div>
    </main>
</body>
</html>
