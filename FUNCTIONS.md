# FUNCTIONS.md - Function Registry

## Auth Class Functions

Function Name: __construct, Function Line: 12, File Path: pixels/src/classes/Auth.php, Description: Initialize Auth class with database instance for user authentication operations, Parameters: none, Return Type: void, Example Usage: $auth = new Auth(), Parent Function: none, Related Function: none

Function Name: login, Function Line: 19, File Path: pixels/src/classes/Auth.php, Description: Authenticate user with username and password including rate limiting and account lockout protection, Parameters: $username, $password, Return Type: array, Example Usage: $user = $auth->login('admin', 'password'), Parent Function: none, Related Function: logout

Function Name: logout, Function Line: 79, File Path: pixels/src/classes/Auth.php, Description: Logout current user by destroying session and clearing authentication state, Parameters: none, Return Type: void, Example Usage: $auth->logout(), Parent Function: none, Related Function: login

Function Name: getCurrentUser, Function Line: 87, File Path: pixels/src/classes/Auth.php, Description: Get current authenticated user information including ID, username, email, and timestamps, Parameters: none, Return Type: array|null, Example Usage: $user = $auth->getCurrentUser(), Parent Function: none, Related Function: isAuthenticated

Function Name: isAuthenticated, Function Line: 103, File Path: pixels/src/classes/Auth.php, Description: Check if current user is authenticated and session is valid, Parameters: none, Return Type: boolean, Example Usage: if ($auth->isAuthenticated()) { ... }, Parent Function: none, Related Function: requireAuth

Function Name: changePassword, Function Line: 111, File Path: pixels/src/classes/Auth.php, Description: Change user password with current password verification and new password validation, Parameters: $userId, $currentPassword, $newPassword, Return Type: boolean, Example Usage: $auth->changePassword($userId, 'oldpass', 'newpass'), Parent Function: none, Related Function: none

Function Name: incrementFailedAttempts, Function Line: 150, File Path: pixels/src/classes/Auth.php, Description: Increment failed login attempts counter and lock account after 5 attempts for 30 minutes, Parameters: $userId, Return Type: void, Example Usage: $this->incrementFailedAttempts($userId), Parent Function: login, Related Function: resetFailedAttempts

Function Name: resetFailedAttempts, Function Line: 175, File Path: pixels/src/classes/Auth.php, Description: Reset failed login attempts counter and unlock account after successful login, Parameters: $userId, Return Type: void, Example Usage: $this->resetFailedAttempts($userId), Parent Function: login, Related Function: incrementFailedAttempts

Function Name: requireAuth, Function Line: 186, File Path: pixels/src/classes/Auth.php, Description: Require authentication and redirect to login page if not authenticated with context-aware redirect URLs, Parameters: $redirectUrl, Return Type: void, Example Usage: $auth->requireAuth(), Parent Function: none, Related Function: isAuthenticated

Function Name: createUser, Function Line: 212, File Path: pixels/src/classes/Auth.php, Description: Create new user account with input validation and duplicate username checking, Parameters: $username, $password, $email, Return Type: integer, Example Usage: $userId = $auth->createUser('newuser', 'password', '<EMAIL>'), Parent Function: none, Related Function: none

Function Name: generateApiToken, Function Line: 256, File Path: pixels/src/classes/Auth.php, Description: Generate secure 64-character hex API token for user authentication in API requests, Parameters: $userId, Return Type: string, Example Usage: $token = $auth->generateApiToken($userId), Parent Function: none, Related Function: validateApiToken

Function Name: validateApiToken, Function Line: 271, File Path: pixels/src/classes/Auth.php, Description: Validate API token and return user information if token belongs to admin user, Parameters: $token, Return Type: array|false, Example Usage: $user = $auth->validateApiToken($token), Parent Function: none, Related Function: generateApiToken

Function Name: getUserByApiToken, Function Line: 288, File Path: pixels/src/classes/Auth.php, Description: Get user information by API token for authentication and authorization purposes, Parameters: $token, Return Type: array|false, Example Usage: $user = $auth->getUserByApiToken($token), Parent Function: none, Related Function: validateApiToken

## Database Class Functions

Function Name: __construct, Function Line: 12, File Path: pixels/src/classes/Database.php, Description: Initialize database connection to SQLite database with singleton pattern enforcement, Parameters: none, Return Type: void, Example Usage: private constructor called by getInstance(), Parent Function: getInstance, Related Function: connect

Function Name: getInstance, Function Line: 18, File Path: pixels/src/classes/Database.php, Description: Get singleton database instance ensuring only one connection throughout application lifecycle, Parameters: none, Return Type: Database, Example Usage: $db = Database::getInstance(), Parent Function: none, Related Function: __construct

Function Name: connect, Function Line: 28, File Path: pixels/src/classes/Database.php, Description: Establish SQLite database connection with error mode and foreign key constraints enabled, Parameters: none, Return Type: void, Example Usage: $this->connect(), Parent Function: __construct, Related Function: none

Function Name: getPdo, Function Line: 46, File Path: pixels/src/classes/Database.php, Description: Get PDO instance for direct database operations when needed, Parameters: none, Return Type: PDO, Example Usage: $pdo = $db->getPdo(), Parent Function: none, Related Function: none

Function Name: query, Function Line: 53, File Path: pixels/src/classes/Database.php, Description: Execute prepared statement with parameters and return statement object with error handling, Parameters: $sql, $params, Return Type: PDOStatement, Example Usage: $stmt = $db->query('SELECT * FROM users WHERE id = ?', [$id]), Parent Function: none, Related Function: fetchOne

Function Name: fetchOne, Function Line: 67, File Path: pixels/src/classes/Database.php, Description: Execute query and fetch single row as associative array, Parameters: $sql, $params, Return Type: array|false, Example Usage: $user = $db->fetchOne('SELECT * FROM users WHERE id = ?', [$id]), Parent Function: none, Related Function: fetchAll

Function Name: fetchAll, Function Line: 75, File Path: pixels/src/classes/Database.php, Description: Execute query and fetch all rows as array of associative arrays, Parameters: $sql, $params, Return Type: array, Example Usage: $posts = $db->fetchAll('SELECT * FROM posts'), Parent Function: none, Related Function: fetchOne

Function Name: lastInsertId, Function Line: 83, File Path: pixels/src/classes/Database.php, Description: Get the ID of the last inserted row for auto-increment columns, Parameters: none, Return Type: string, Example Usage: $id = $db->lastInsertId(), Parent Function: none, Related Function: query

Function Name: beginTransaction, Function Line: 90, File Path: pixels/src/classes/Database.php, Description: Begin database transaction for atomic operations, Parameters: none, Return Type: boolean, Example Usage: $db->beginTransaction(), Parent Function: none, Related Function: commit

Function Name: commit, Function Line: 97, File Path: pixels/src/classes/Database.php, Description: Commit current database transaction, Parameters: none, Return Type: boolean, Example Usage: $db->commit(), Parent Function: none, Related Function: rollback

Function Name: rollback, Function Line: 104, File Path: pixels/src/classes/Database.php, Description: Rollback current database transaction on error, Parameters: none, Return Type: boolean, Example Usage: $db->rollback(), Parent Function: none, Related Function: commit

Function Name: isInstalled, Function Line: 111, File Path: pixels/src/classes/Database.php, Description: Check if database exists and contains required tables for CMS installation verification, Parameters: none, Return Type: boolean, Example Usage: if ($db->isInstalled()) { ... }, Parent Function: none, Related Function: none

Function Name: __clone, Function Line: 127, File Path: pixels/src/classes/Database.php, Description: Prevent cloning of singleton database instance, Parameters: none, Return Type: void, Example Usage: automatic, Parent Function: none, Related Function: __wakeup

Function Name: __wakeup, Function Line: 132, File Path: pixels/src/classes/Database.php, Description: Prevent unserialization of singleton database instance, Parameters: none, Return Type: void, Example Usage: automatic, Parent Function: none, Related Function: __clone

## ImageUpload Class Functions

Function Name: __construct, Function Line: 14, File Path: pixels/src/classes/ImageUpload.php, Description: Initialize image upload configuration with directory paths, file size limits, and create upload directory structure, Parameters: none, Return Type: void, Example Usage: $imageUpload = new ImageUpload(), Parent Function: none, Related Function: createUploadDirectory

Function Name: createUploadDirectory, Function Line: 26, File Path: pixels/src/classes/ImageUpload.php, Description: Create upload directory structure with proper permissions and security htaccess file, Parameters: none, Return Type: void, Example Usage: $this->createUploadDirectory(), Parent Function: __construct, Related Function: none

Function Name: uploadImage, Function Line: 48, File Path: pixels/src/classes/ImageUpload.php, Description: Upload and process image with validation, resizing, and optional cropping functionality, Parameters: $file, $cropData, Return Type: string, Example Usage: $imagePath = $imageUpload->uploadImage($_FILES['image'], $cropData), Parent Function: none, Related Function: validateUpload

Function Name: validateUpload, Function Line: 87, File Path: pixels/src/classes/ImageUpload.php, Description: Validate uploaded file for size, type, extension, and security checks, Parameters: $file, Return Type: void, Example Usage: $this->validateUpload($file), Parent Function: uploadImage, Related Function: none

Function Name: generateUniqueFilename, Function Line: 134, File Path: pixels/src/classes/ImageUpload.php, Description: Generate unique filename with timestamp and random bytes to prevent conflicts, Parameters: $originalName, $extension, Return Type: string, Example Usage: $filename = $this->generateUniqueFilename('image', 'jpg'), Parent Function: uploadImage, Related Function: none

Function Name: processImage, Function Line: 169, File Path: pixels/src/classes/ImageUpload.php, Description: Resize image if dimensions exceed maximum allowed size while maintaining aspect ratio, Parameters: $filepath, Return Type: void, Example Usage: $this->processImage($filepath), Parent Function: uploadImage, Related Function: cropImage

Function Name: cropImage, Function Line: 216, File Path: pixels/src/classes/ImageUpload.php, Description: Crop image based on provided crop coordinates and dimensions, Parameters: $filepath, $cropData, Return Type: void, Example Usage: $this->cropImage($filepath, $cropData), Parent Function: uploadImage, Related Function: processImage

Function Name: createImageFromFile, Function Line: 257, File Path: pixels/src/classes/ImageUpload.php, Description: Create GD image resource from file based on MIME type, Parameters: $filepath, $mimeType, Return Type: resource|false, Example Usage: $image = $this->createImageFromFile($filepath, $mimeType), Parent Function: processImage, Related Function: saveImage

Function Name: saveImage, Function Line: 275, File Path: pixels/src/classes/ImageUpload.php, Description: Save GD image resource to file with appropriate format and quality settings, Parameters: $imageResource, $filepath, $mimeType, $quality, Return Type: boolean, Example Usage: $this->saveImage($image, $filepath, $mimeType, 85), Parent Function: processImage, Related Function: createImageFromFile

Function Name: deleteImage, Function Line: 295, File Path: pixels/src/classes/ImageUpload.php, Description: Delete uploaded image file with security checks for allowed directories, Parameters: $imagePath, Return Type: boolean, Example Usage: $imageUpload->deleteImage('/uploads/thumbnails/image.jpg'), Parent Function: none, Related Function: none

Function Name: getImageDimensions, Function Line: 312, File Path: pixels/src/classes/ImageUpload.php, Description: Get image dimensions and MIME type information for uploaded image, Parameters: $imagePath, Return Type: array|false, Example Usage: $dimensions = $imageUpload->getImageDimensions($imagePath), Parent Function: none, Related Function: none

Function Name: isGdAvailable, Function Line: 330, File Path: pixels/src/classes/ImageUpload.php, Description: Check if GD extension is available for image processing operations, Parameters: none, Return Type: boolean, Example Usage: if (ImageUpload::isGdAvailable()) { ... }, Parent Function: none, Related Function: getSupportedFormats

Function Name: getSupportedFormats, Function Line: 337, File Path: pixels/src/classes/ImageUpload.php, Description: Get list of supported image formats based on available GD functions, Parameters: none, Return Type: array, Example Usage: $formats = ImageUpload::getSupportedFormats(), Parent Function: none, Related Function: isGdAvailable

Function Name: validateCropData, Function Line: 354, File Path: pixels/src/classes/ImageUpload.php, Description: Validate crop data coordinates and dimensions against image boundaries, Parameters: $cropData, $imageWidth, $imageHeight, Return Type: boolean, Example Usage: if ($imageUpload->validateCropData($cropData, $width, $height)) { ... }, Parent Function: none, Related Function: cropImage

## Post Class Functions

Function Name: __construct, Function Line: 12, File Path: pixels/src/classes/Post.php, Description: Initialize Post class with database instance for blog post management operations, Parameters: none, Return Type: void, Example Usage: $postManager = new Post(), Parent Function: none, Related Function: none

Function Name: getValidCategories, Function Line: 17, File Path: pixels/src/classes/Post.php, Description: Get array of valid post categories for validation and form options, Parameters: none, Return Type: array, Example Usage: $categories = Post::getValidCategories(), Parent Function: none, Related Function: none

Function Name: getAllPosts, Function Line: 25, File Path: pixels/src/classes/Post.php, Description: Get all posts with pagination support and optional published-only filtering, Parameters: $page, $perPage, $publishedOnly, Return Type: array, Example Usage: $result = $postManager->getAllPosts(1, 10, true), Parent Function: none, Related Function: getPostsByCategory

Function Name: getPostsByCategory, Function Line: 62, File Path: pixels/src/classes/Post.php, Description: Get posts filtered by category with pagination and optional published-only filtering, Parameters: $category, $page, $perPage, $publishedOnly, Return Type: array, Example Usage: $result = $postManager->getPostsByCategory('tech', 1, 10, true), Parent Function: none, Related Function: getAllPosts

Function Name: getPost, Function Line: 113, File Path: pixels/src/classes/Post.php, Description: Get single post by ID with optional published-only filtering and author information, Parameters: $id, $publishedOnly, Return Type: array|false, Example Usage: $post = $postManager->getPost(123, true), Parent Function: none, Related Function: getPostBySlug

Function Name: getPostBySlug, Function Line: 130, File Path: pixels/src/classes/Post.php, Description: Get single post by category and slug for SEO-friendly URLs with author information, Parameters: $category, $slug, $publishedOnly, Return Type: array|false, Example Usage: $post = $postManager->getPostBySlug('tech', 'my-post', true), Parent Function: none, Related Function: getPost

Function Name: createPost, Function Line: 147, File Path: pixels/src/classes/Post.php, Description: Create new blog post with required fields validation including description, thumbnail, and optional sources, Parameters: $title, $content, $isDraft, $authorId, $category, $thumbnailUrl, $description, $sources, Return Type: integer, Example Usage: $postId = $postManager->createPost($title, $content, false, $userId, 'tech', $thumbnailUrl, $description, $sources), Parent Function: none, Related Function: updatePost

Function Name: updatePost, Function Line: 258, File Path: pixels/src/classes/Post.php, Description: Update existing blog post with validation for all fields and automatic slug regeneration if title changes, Parameters: $id, $title, $content, $isDraft, $category, $thumbnailUrl, $description, $sources, Return Type: boolean, Example Usage: $postManager->updatePost($id, $title, $content, false, 'tech', $thumbnailUrl, $description, $sources), Parent Function: none, Related Function: createPost

Function Name: validateSources, Function Line: 406, File Path: pixels/src/classes/Post.php, Description: Validate comma-separated source URLs with basic URL format checking and sanitization, Parameters: $sources, Return Type: string|null, Example Usage: $validSources = $postManager->validateSources($sources), Parent Function: createPost, Related Function: formatSources

Function Name: calculateReadingTime, Function Line: 435, File Path: pixels/src/classes/Post.php, Description: Calculate estimated reading time based on word count and average reading speed of 250 words per minute, Parameters: $content, Return Type: integer, Example Usage: $readingTime = $postManager->calculateReadingTime($content), Parent Function: none, Related Function: none

Function Name: formatSources, Function Line: 463, File Path: pixels/src/classes/Post.php, Description: Format comma-separated source URLs into array of objects with URL and domain for display, Parameters: $sources, Return Type: array, Example Usage: $formattedSources = $postManager->formatSources($sources), Parent Function: none, Related Function: validateSources

Function Name: deletePost, Function Line: 496, File Path: pixels/src/classes/Post.php, Description: Delete blog post by ID with existence validation, Parameters: $id, Return Type: boolean, Example Usage: $postManager->deletePost(123), Parent Function: none, Related Function: none

Function Name: toggleDraftStatus, Function Line: 512, File Path: pixels/src/classes/Post.php, Description: Toggle post between draft and published status with automatic timestamp update, Parameters: $id, Return Type: integer, Example Usage: $newStatus = $postManager->toggleDraftStatus($id), Parent Function: none, Related Function: none

Function Name: getRecentPosts, Function Line: 531, File Path: pixels/src/classes/Post.php, Description: Get most recently updated posts for dashboard display with author information, Parameters: $limit, Return Type: array, Example Usage: $recentPosts = $postManager->getRecentPosts(5), Parent Function: none, Related Function: none

Function Name: getStatistics, Function Line: 546, File Path: pixels/src/classes/Post.php, Description: Get post statistics including total, published, and draft counts for dashboard, Parameters: none, Return Type: array, Example Usage: $stats = $postManager->getStatistics(), Parent Function: none, Related Function: none

Function Name: searchPosts, Function Line: 571, File Path: pixels/src/classes/Post.php, Description: Search posts by title and content with pagination and optional published-only filtering, Parameters: $query, $publishedOnly, $page, $perPage, Return Type: array, Example Usage: $results = $postManager->searchPosts('search term', true, 1, 10), Parent Function: none, Related Function: none

Function Name: isValidThumbnailUrl, Function Line: 621, File Path: pixels/src/classes/Post.php, Description: Validate thumbnail URL accepting both relative paths and full URLs with security checks, Parameters: $url, Return Type: boolean, Example Usage: if (Post::isValidThumbnailUrl($url)) { ... }, Parent Function: createPost, Related Function: updatePost

## Response Class Functions

Function Name: setHeader, Function Line: 13, File Path: pixels/src/classes/Response.php, Description: Set HTTP response header with name and value, Parameters: $name, $value, Return Type: void, Example Usage: Response::setHeader('Content-Type', 'application/json'), Parent Function: none, Related Function: none

Function Name: setCompression, Function Line: 20, File Path: pixels/src/classes/Response.php, Description: Enable or disable response compression for output buffering, Parameters: $enabled, Return Type: void, Example Usage: Response::setCompression(true), Parent Function: none, Related Function: startOutput

Function Name: startOutput, Function Line: 27, File Path: pixels/src/classes/Response.php, Description: Start output buffering with optional compression if supported and enabled, Parameters: none, Return Type: void, Example Usage: Response::startOutput(), Parent Function: none, Related Function: compressOutput

Function Name: canCompress, Function Line: 38, File Path: pixels/src/classes/Response.php, Description: Check if compression is supported by server and client browser, Parameters: none, Return Type: boolean, Example Usage: if (Response::canCompress()) { ... }, Parent Function: startOutput, Related Function: compressOutput

Function Name: compressOutput, Function Line: 47, File Path: pixels/src/classes/Response.php, Description: Compress output buffer using gzip for supported content types and appropriate response codes, Parameters: $buffer, Return Type: string, Example Usage: ob_start([Response::class, 'compressOutput']), Parent Function: startOutput, Related Function: canCompress

Function Name: setCacheHeaders, Function Line: 90, File Path: pixels/src/classes/Response.php, Description: Set cache control headers for dynamic content with max age and public/private settings, Parameters: $maxAge, $public, Return Type: void, Example Usage: Response::setCacheHeaders(3600, true), Parent Function: none, Related Function: none

Function Name: setContentType, Function Line: 106, File Path: pixels/src/classes/Response.php, Description: Set content type header with optional charset specification, Parameters: $type, $charset, Return Type: void, Example Usage: Response::setContentType('application/json', 'UTF-8'), Parent Function: none, Related Function: setHeader

Function Name: json, Function Line: 113, File Path: pixels/src/classes/Response.php, Description: Send JSON response with data and HTTP status code, Parameters: $data, $statusCode, Return Type: void, Example Usage: Response::json(['status' => 'success'], 200), Parent Function: none, Related Function: setContentType

Function Name: redirect, Function Line: 124, File Path: pixels/src/classes/Response.php, Description: Send HTTP redirect response with optional permanent redirect flag, Parameters: $url, $permanent, Return Type: void, Example Usage: Response::redirect('/admin/dashboard', false), Parent Function: none, Related Function: none

Function Name: download, Function Line: 134, File Path: pixels/src/classes/Response.php, Description: Send file download response with proper headers for file attachment, Parameters: $filePath, $fileName, $mimeType, Return Type: void, Example Usage: Response::download('/path/to/file.pdf', 'document.pdf'), Parent Function: none, Related Function: getMimeType

Function Name: getMimeType, Function Line: 156, File Path: pixels/src/classes/Response.php, Description: Get MIME type for file based on extension with fallback to octet-stream, Parameters: $filePath, Return Type: string, Example Usage: $mimeType = Response::getMimeType('/path/to/file.jpg'), Parent Function: download, Related Function: none

Function Name: send, Function Line: 189, File Path: pixels/src/classes/Response.php, Description: End output buffering and send response to client, Parameters: none, Return Type: void, Example Usage: Response::send(), Parent Function: none, Related Function: none

Function Name: setFileUploadHeaders, Function Line: 198, File Path: pixels/src/classes/Response.php, Description: Set security headers specifically for file upload responses, Parameters: none, Return Type: void, Example Usage: Response::setFileUploadHeaders(), Parent Function: none, Related Function: setHeader

Function Name: handleConditionalRequest, Function Line: 206, File Path: pixels/src/classes/Response.php, Description: Handle conditional HTTP requests with ETag and Last-Modified headers for caching, Parameters: $lastModified, $etag, Return Type: void, Example Usage: Response::handleConditionalRequest(filemtime($file), md5($file)), Parent Function: none, Related Function: setHeader

## Router Class Functions

Function Name: init, Function Line: 13, File Path: pixels/src/classes/Router.php, Description: Initialize router with default routes for SEO URLs and page routing without htaccess dependency, Parameters: none, Return Type: void, Example Usage: Router::init(), Parent Function: none, Related Function: addRoute

Function Name: addRoute, Function Line: 24, File Path: pixels/src/classes/Router.php, Description: Add new route with HTTP method, pattern, target file, and optional parameters, Parameters: $method, $pattern, $target, $params, Return Type: void, Example Usage: Router::addRoute('GET', '/^\/api\/posts\/?$/', 'api/posts.php'), Parent Function: none, Related Function: route

Function Name: route, Function Line: 36, File Path: pixels/src/classes/Router.php, Description: Route current request to appropriate handler based on URL pattern matching and file existence, Parameters: none, Return Type: void, Example Usage: Router::route(), Parent Function: none, Related Function: sendError

Function Name: isStaticFile, Function Line: 119, File Path: pixels/src/classes/Router.php, Description: Check if requested file is a static asset based on file extension, Parameters: $path, Return Type: boolean, Example Usage: if (Router::isStaticFile($path)) { ... }, Parent Function: route, Related Function: serveStaticFile

Function Name: serveStaticFile, Function Line: 128, File Path: pixels/src/classes/Router.php, Description: Serve static files with proper MIME types, cache headers, and optional compression, Parameters: $filePath, Return Type: void, Example Usage: Router::serveStaticFile($filePath), Parent Function: route, Related Function: isStaticFile

Function Name: sendError, Function Line: 159, File Path: pixels/src/classes/Router.php, Description: Send HTTP error response with appropriate status code and error page, Parameters: $code, $message, Return Type: void, Example Usage: Router::sendError(404, 'Not Found'), Parent Function: route, Related Function: none

Function Name: getCurrentRoute, Function Line: 193, File Path: pixels/src/classes/Router.php, Description: Get information about currently matched route for debugging and context, Parameters: none, Return Type: array|null, Example Usage: $route = Router::getCurrentRoute(), Parent Function: none, Related Function: none

Function Name: url, Function Line: 200, File Path: pixels/src/classes/Router.php, Description: Generate full URL with protocol and host for given path, Parameters: $path, Return Type: string, Example Usage: $url = Router::url('/admin/posts'), Parent Function: none, Related Function: none

## Security Class Functions

Function Name: generateCSRFToken, Function Line: 12, File Path: pixels/src/classes/Security.php, Description: Generate cryptographically secure CSRF token for form protection, Parameters: none, Return Type: string, Example Usage: $token = Security::generateCSRFToken(), Parent Function: none, Related Function: validateCSRFToken

Function Name: validateCSRFToken, Function Line: 20, File Path: pixels/src/classes/Security.php, Description: Validate CSRF token against session token using timing-safe comparison, Parameters: $token, Return Type: boolean, Example Usage: if (Security::validateCSRFToken($token)) { ... }, Parent Function: none, Related Function: generateCSRFToken

Function Name: sanitizeInput, Function Line: 30, File Path: pixels/src/classes/Security.php, Description: Sanitize input data by HTML encoding special characters for XSS prevention, Parameters: $input, Return Type: string|array, Example Usage: $clean = Security::sanitizeInput($_POST['data']), Parent Function: none, Related Function: escape

Function Name: validateEmail, Function Line: 40, File Path: pixels/src/classes/Security.php, Description: Validate email address format using PHP filter validation, Parameters: $email, Return Type: boolean, Example Usage: if (Security::validateEmail($email)) { ... }, Parent Function: none, Related Function: none

Function Name: validateLength, Function Line: 47, File Path: pixels/src/classes/Security.php, Description: Validate string length against minimum and maximum constraints, Parameters: $string, $min, $max, Return Type: boolean, Example Usage: if (Security::validateLength($input, 1, 255)) { ... }, Parent Function: none, Related Function: none

Function Name: generateRandomString, Function Line: 55, File Path: pixels/src/classes/Security.php, Description: Generate cryptographically secure random string of specified length, Parameters: $length, Return Type: string, Example Usage: $random = Security::generateRandomString(32), Parent Function: none, Related Function: none

Function Name: setSecurityHeaders, Function Line: 62, File Path: pixels/src/classes/Security.php, Description: Set comprehensive security headers including XSS protection, CSP, and HSTS, Parameters: none, Return Type: void, Example Usage: Security::setSecurityHeaders(), Parent Function: none, Related Function: none

Function Name: isFileAccessAllowed, Function Line: 91, File Path: pixels/src/classes/Security.php, Description: Check if file access is allowed based on extension and directory restrictions, Parameters: $filePath, Return Type: boolean, Example Usage: if (Security::isFileAccessAllowed($path)) { ... }, Parent Function: none, Related Function: none

Function Name: isExecutableFile, Function Line: 111, File Path: pixels/src/classes/Security.php, Description: Check if file has executable extension that could pose security risk, Parameters: $fileName, Return Type: boolean, Example Usage: if (Security::isExecutableFile($filename)) { ... }, Parent Function: none, Related Function: none

Function Name: enforceHTTPS, Function Line: 119, File Path: pixels/src/classes/Security.php, Description: Force HTTPS redirect if configured and not already using secure connection, Parameters: $force, Return Type: void, Example Usage: Security::enforceHTTPS(true), Parent Function: none, Related Function: none

Function Name: getClientIP, Function Line: 130, File Path: pixels/src/classes/Security.php, Description: Get client IP address with support for proxy headers and validation, Parameters: none, Return Type: string, Example Usage: $ip = Security::getClientIP(), Parent Function: none, Related Function: none

Function Name: checkRateLimit, Function Line: 152, File Path: pixels/src/classes/Security.php, Description: Check rate limiting for login attempts based on IP address and time window, Parameters: $ip, $maxAttempts, $timeWindow, Return Type: boolean, Example Usage: if (Security::checkRateLimit($ip)) { ... }, Parent Function: none, Related Function: logLoginAttempt

Function Name: logLoginAttempt, Function Line: 173, File Path: pixels/src/classes/Security.php, Description: Log login attempt with IP address, username, and success status for security monitoring, Parameters: $ip, $username, $success, Return Type: void, Example Usage: Security::logLoginAttempt($ip, $username, true), Parent Function: none, Related Function: checkRateLimit

Function Name: escape, Function Line: 184, File Path: pixels/src/classes/Security.php, Description: Escape string for safe HTML output using htmlspecialchars, Parameters: $string, Return Type: string, Example Usage: echo Security::escape($userInput), Parent Function: none, Related Function: sanitizeInput

Function Name: validatePassword, Function Line: 191, File Path: pixels/src/classes/Security.php, Description: Validate password strength requiring minimum 8 characters with letters and numbers, Parameters: $password, Return Type: boolean, Example Usage: if (Security::validatePassword($password)) { ... }, Parent Function: none, Related Function: none

## Session Class Functions

Function Name: start, Function Line: 12, File Path: pixels/src/classes/Session.php, Description: Start secure session with security configurations and periodic ID regeneration, Parameters: none, Return Type: void, Example Usage: Session::start(), Parent Function: none, Related Function: regenerateId

Function Name: regenerateId, Function Line: 43, File Path: pixels/src/classes/Session.php, Description: Regenerate session ID for security and update database session record, Parameters: none, Return Type: void, Example Usage: Session::regenerateId(), Parent Function: start, Related Function: storeInDatabase

Function Name: set, Function Line: 62, File Path: pixels/src/classes/Session.php, Description: Set session variable with automatic session start, Parameters: $key, $value, Return Type: void, Example Usage: Session::set('user_id', 123), Parent Function: none, Related Function: get

Function Name: get, Function Line: 70, File Path: pixels/src/classes/Session.php, Description: Get session variable with optional default value and automatic session start, Parameters: $key, $default, Return Type: mixed, Example Usage: $userId = Session::get('user_id'), Parent Function: none, Related Function: set

Function Name: has, Function Line: 78, File Path: pixels/src/classes/Session.php, Description: Check if session variable exists with automatic session start, Parameters: $key, Return Type: boolean, Example Usage: if (Session::has('user_id')) { ... }, Parent Function: none, Related Function: get

Function Name: remove, Function Line: 86, File Path: pixels/src/classes/Session.php, Description: Remove session variable with automatic session start, Parameters: $key, Return Type: void, Example Usage: Session::remove('temp_data'), Parent Function: none, Related Function: set

Function Name: destroy, Function Line: 94, File Path: pixels/src/classes/Session.php, Description: Destroy session completely including database record and session cookie, Parameters: none, Return Type: void, Example Usage: Session::destroy(), Parent Function: none, Related Function: logout

Function Name: storeInDatabase, Function Line: 122, File Path: pixels/src/classes/Session.php, Description: Store session information in database for logged-in users with IP and user agent tracking, Parameters: $userId, Return Type: void, Example Usage: Session::storeInDatabase($userId), Parent Function: login, Related Function: validateFromDatabase

Function Name: validateFromDatabase, Function Line: 141, File Path: pixels/src/classes/Session.php, Description: Validate session against database record and check for expiration, Parameters: none, Return Type: boolean, Example Usage: if (Session::validateFromDatabase()) { ... }, Parent Function: isLoggedIn, Related Function: storeInDatabase

Function Name: cleanExpiredSessions, Function Line: 186, File Path: pixels/src/classes/Session.php, Description: Clean expired sessions from database based on configured session lifetime, Parameters: $maxAge, Return Type: void, Example Usage: Session::cleanExpiredSessions(), Parent Function: none, Related Function: none

Function Name: isLoggedIn, Function Line: 200, File Path: pixels/src/classes/Session.php, Description: Check if user is logged in with valid session in database, Parameters: none, Return Type: boolean, Example Usage: if (Session::isLoggedIn()) { ... }, Parent Function: none, Related Function: validateFromDatabase

Function Name: getUserId, Function Line: 207, File Path: pixels/src/classes/Session.php, Description: Get logged-in user ID if session is valid, Parameters: none, Return Type: integer|null, Example Usage: $userId = Session::getUserId(), Parent Function: none, Related Function: isLoggedIn

Function Name: login, Function Line: 214, File Path: pixels/src/classes/Session.php, Description: Login user by setting session variables and storing in database, Parameters: $userId, Return Type: void, Example Usage: Session::login($userId), Parent Function: none, Related Function: storeInDatabase

Function Name: logout, Function Line: 223, File Path: pixels/src/classes/Session.php, Description: Logout user by destroying session completely, Parameters: none, Return Type: void, Example Usage: Session::logout(), Parent Function: none, Related Function: destroy

Function Name: getTimeRemaining, Function Line: 230, File Path: pixels/src/classes/Session.php, Description: Get remaining session time in seconds before expiration, Parameters: none, Return Type: integer, Example Usage: $remaining = Session::getTimeRemaining(), Parent Function: none, Related Function: isAboutToExpire

Function Name: isAboutToExpire, Function Line: 256, File Path: pixels/src/classes/Session.php, Description: Check if session is about to expire within warning time threshold, Parameters: $warningTime, Return Type: boolean, Example Usage: if (Session::isAboutToExpire()) { ... }, Parent Function: none, Related Function: getTimeRemaining

Function Name: extend, Function Line: 264, File Path: pixels/src/classes/Session.php, Description: Extend session by updating last activity timestamp in database, Parameters: none, Return Type: void, Example Usage: Session::extend(), Parent Function: none, Related Function: none

Function Name: getSessionInfo, Function Line: 278, File Path: pixels/src/classes/Session.php, Description: Get detailed session information for debugging including timestamps and expiration status, Parameters: none, Return Type: array|null, Example Usage: $info = Session::getSessionInfo(), Parent Function: none, Related Function: none

## Config Class Functions

Function Name: get, Function Line: 117, File Path: pixels/src/includes/config.php, Description: Get configuration value by key with optional default value, Parameters: $key, $default, Return Type: mixed, Example Usage: $value = Config::get('SESSION_LIFETIME', 3600), Parent Function: none, Related Function: none

Function Name: isBlockedExtension, Function Line: 124, File Path: pixels/src/includes/config.php, Description: Check if file extension is in blocked extensions list for security, Parameters: $extension, Return Type: boolean, Example Usage: if (Config::isBlockedExtension($ext)) { ... }, Parent Function: none, Related Function: isExecutableExtension

Function Name: isBlockedDirectory, Function Line: 131, File Path: pixels/src/includes/config.php, Description: Check if directory path is in blocked directories list for security, Parameters: $path, Return Type: boolean, Example Usage: if (Config::isBlockedDirectory($path)) { ... }, Parent Function: none, Related Function: isBlockedExtension

Function Name: isExecutableExtension, Function Line: 143, File Path: pixels/src/includes/config.php, Description: Check if file extension is executable and potentially dangerous, Parameters: $extension, Return Type: boolean, Example Usage: if (Config::isExecutableExtension($ext)) { ... }, Parent Function: none, Related Function: isBlockedExtension

Function Name: getMimeType, Function Line: 150, File Path: pixels/src/includes/config.php, Description: Get MIME type for file extension with fallback to octet-stream, Parameters: $extension, Return Type: string, Example Usage: $mimeType = Config::getMimeType('jpg'), Parent Function: none, Related Function: none

Function Name: getCacheTime, Function Line: 157, File Path: pixels/src/includes/config.php, Description: Get cache time in seconds for file type based on extension, Parameters: $extension, Return Type: integer, Example Usage: $cacheTime = Config::getCacheTime('css'), Parent Function: none, Related Function: none

Function Name: isCompressible, Function Line: 172, File Path: pixels/src/includes/config.php, Description: Check if content type is compressible for gzip compression, Parameters: $contentType, Return Type: boolean, Example Usage: if (Config::isCompressible($contentType)) { ... }, Parent Function: none, Related Function: none

## Utility Functions

Function Name: formatDate, Function Line: 10, File Path: pixels/src/includes/functions.php, Description: Format date string using specified format or default format, Parameters: $date, $format, Return Type: string, Example Usage: echo formatDate($post['created_at']), Parent Function: none, Related Function: timeAgo

Function Name: truncateText, Function Line: 18, File Path: pixels/src/includes/functions.php, Description: Truncate text to specified length with optional suffix, Parameters: $text, $length, $suffix, Return Type: string, Example Usage: echo truncateText($content, 150), Parent Function: none, Related Function: generateExcerpt

Function Name: generateExcerpt, Function Line: 30, File Path: pixels/src/includes/functions.php, Description: Generate excerpt from content by stripping HTML and truncating to word boundary, Parameters: $content, $length, Return Type: string, Example Usage: echo generateExcerpt($post['content'], 200), Parent Function: none, Related Function: truncateText

Function Name: displayFlashMessages, Function Line: 53, File Path: pixels/src/includes/functions.php, Description: Display and clear flash messages from session with proper HTML formatting, Parameters: none, Return Type: string, Example Usage: echo displayFlashMessages(), Parent Function: none, Related Function: addFlashMessage

Function Name: addFlashMessage, Function Line: 79, File Path: pixels/src/includes/functions.php, Description: Add flash message to session for display on next page load, Parameters: $message, $type, Return Type: void, Example Usage: addFlashMessage('Post saved successfully', 'success'), Parent Function: none, Related Function: displayFlashMessages

Function Name: containsHtml, Function Line: 89, File Path: pixels/src/includes/functions.php, Description: Check if string contains HTML tags by comparing with stripped version, Parameters: $string, Return Type: boolean, Example Usage: if (containsHtml($content)) { ... }, Parent Function: none, Related Function: parseMarkdown

Function Name: parseMarkdown, Function Line: 98, File Path: pixels/src/includes/functions.php, Description: Convert markdown text to HTML with support for headers, formatting, links, images, code blocks, and lists, Parameters: $text, Return Type: string, Example Usage: echo parseMarkdown($post['content']), Parent Function: none, Related Function: containsHtml

Function Name: nl2br_safe, Function Line: 208, File Path: pixels/src/includes/functions.php, Description: Convert newlines to HTML breaks with XSS protection, Parameters: $string, Return Type: string, Example Usage: echo nl2br_safe($text), Parent Function: none, Related Function: none

Function Name: csrfTokenField, Function Line: 216, File Path: pixels/src/includes/functions.php, Description: Generate CSRF token hidden input field for forms, Parameters: none, Return Type: string, Example Usage: echo csrfTokenField(), Parent Function: none, Related Function: validateCSRF

Function Name: validateCSRF, Function Line: 227, File Path: pixels/src/includes/functions.php, Description: Validate CSRF token from POST data and throw exception if invalid, Parameters: none, Return Type: void, Example Usage: validateCSRF(), Parent Function: none, Related Function: csrfTokenField

Function Name: getCurrentPage, Function Line: 240, File Path: pixels/src/includes/functions.php, Description: Get current page number from query string with validation, Parameters: none, Return Type: integer, Example Usage: $page = getCurrentPage(), Parent Function: none, Related Function: none

Function Name: buildUrl, Function Line: 249, File Path: pixels/src/includes/functions.php, Description: Build URL with query parameters appended properly, Parameters: $baseUrl, $params, Return Type: string, Example Usage: $url = buildUrl('/posts', ['page' => 2]), Parent Function: none, Related Function: none

Function Name: isCurrentPage, Function Line: 264, File Path: pixels/src/includes/functions.php, Description: Check if given path matches current page for navigation highlighting, Parameters: $path, Return Type: boolean, Example Usage: if (isCurrentPage('/admin/posts')) { ... }, Parent Function: none, Related Function: none

Function Name: generateNavigation, Function Line: 275, File Path: pixels/src/includes/functions.php, Description: Generate navigation menu HTML from array of items with active state detection, Parameters: $items, $currentPath, Return Type: string, Example Usage: echo generateNavigation($menuItems), Parent Function: none, Related Function: isCurrentPage

Function Name: logActivity, Function Line: 300, File Path: pixels/src/includes/functions.php, Description: Log user activity to file with timestamp, user ID, IP address, and details, Parameters: $action, $details, $userId, Return Type: void, Example Usage: logActivity('post_created', 'Post ID: 123'), Parent Function: none, Related Function: none

Function Name: validateFileUpload, Function Line: 322, File Path: pixels/src/includes/functions.php, Description: Validate uploaded file for errors, size, and allowed types, Parameters: $file, $allowedTypes, $maxSize, Return Type: boolean, Example Usage: validateFileUpload($_FILES['image'], ['image/jpeg'], 2097152), Parent Function: none, Related Function: none

Function Name: generateRandomFilename, Function Line: 346, File Path: pixels/src/includes/functions.php, Description: Generate random filename with original extension for secure file uploads, Parameters: $originalName, Return Type: string, Example Usage: $filename = generateRandomFilename('photo.jpg'), Parent Function: none, Related Function: none

Function Name: cleanUrl, Function Line: 357, File Path: pixels/src/includes/functions.php, Description: Clean and validate URL for security and format compliance, Parameters: $url, Return Type: string|false, Example Usage: $cleanUrl = cleanUrl($userInput), Parent Function: none, Related Function: none

Function Name: timeAgo, Function Line: 371, File Path: pixels/src/includes/functions.php, Description: Convert datetime to human-readable time ago format, Parameters: $datetime, Return Type: string, Example Usage: echo timeAgo($post['created_at']), Parent Function: none, Related Function: formatDate

Function Name: generateSlug, Function Line: 394, File Path: pixels/src/includes/functions.php, Description: Generate URL-friendly slug from title by removing special characters and spaces, Parameters: $title, Return Type: string, Example Usage: $slug = generateSlug($post['title']), Parent Function: none, Related Function: generateUniqueSlug

Function Name: generatePostUrl, Function Line: 422, File Path: pixels/src/includes/functions.php, Description: Generate SEO-friendly URL for post using category and slug, Parameters: $post, Return Type: string, Example Usage: $url = generatePostUrl($post), Parent Function: none, Related Function: generateSlug

Function Name: isSlugUnique, Function Line: 433, File Path: pixels/src/includes/functions.php, Description: Check if slug is unique in database with optional post ID exclusion, Parameters: $slug, $excludePostId, Return Type: boolean, Example Usage: if (isSlugUnique($slug, $postId)) { ... }, Parent Function: none, Related Function: generateUniqueSlug

Function Name: generateUniqueSlug, Function Line: 452, File Path: pixels/src/includes/functions.php, Description: Generate unique slug by appending counter if slug already exists, Parameters: $title, $excludePostId, Return Type: string, Example Usage: $slug = generateUniqueSlug($title, $postId), Parent Function: none, Related Function: isSlugUnique

## Initialization Functions

Function Name: handleError, Function Line: 73, File Path: pixels/src/includes/init.php, Description: Global error handler for logging errors to file and displaying user-friendly messages, Parameters: $errno, $errstr, $errfile, $errline, Return Type: boolean, Example Usage: set_error_handler('handleError'), Parent Function: none, Related Function: handleException

Function Name: handleException, Function Line: 86, File Path: pixels/src/includes/init.php, Description: Global exception handler for logging uncaught exceptions to file, Parameters: $exception, Return Type: void, Example Usage: set_exception_handler('handleException'), Parent Function: none, Related Function: handleError

Function Name: isAdminArea, Function Line: 107, File Path: pixels/src/includes/init.php, Description: Check if current request is in admin area based on URL path, Parameters: none, Return Type: boolean, Example Usage: if (isAdminArea()) { ... }, Parent Function: none, Related Function: none

Function Name: getBaseUrl, Function Line: 112, File Path: pixels/src/includes/init.php, Description: Get base URL with protocol, host, and script path for absolute URL generation, Parameters: none, Return Type: string, Example Usage: $baseUrl = getBaseUrl(), Parent Function: none, Related Function: none

Function Name: redirect, Function Line: 120, File Path: pixels/src/includes/init.php, Description: Redirect to URL with optional permanent redirect flag, Parameters: $url, $permanent, Return Type: void, Example Usage: redirect('/admin/dashboard'), Parent Function: none, Related Function: none

Function Name: generatePagination, Function Line: 128, File Path: pixels/src/includes/init.php, Description: Generate pagination HTML with previous/next links and page numbers, Parameters: $currentPage, $totalPages, $baseUrl, Return Type: string, Example Usage: echo generatePagination($page, $totalPages, '/posts'), Parent Function: none, Related Function: getCurrentPage

## YamlParser Class Functions

Function Name: parse, Function Line: 18, File Path: pixels/src/classes/YamlParser.php, Description: Parse YAML string into associative array with support for basic YAML syntax including multiline values and nested objects, Parameters: $yamlString, Return Type: array, Example Usage: $config = YamlParser::parse($yamlContent), Parent Function: none, Related Function: parseFile

Function Name: parseFile, Function Line: 103, File Path: pixels/src/classes/YamlParser.php, Description: Parse YAML file and return associative array with error handling for missing files, Parameters: $filePath, Return Type: array, Example Usage: $config = YamlParser::parseFile('/path/to/config.yaml'), Parent Function: none, Related Function: parse

Function Name: validateScraperConfig, Function Line: 119, File Path: pixels/src/classes/YamlParser.php, Description: Validate scraper configuration array for required fields and proper URL format, Parameters: $config, Return Type: array, Example Usage: $errors = YamlParser::validateScraperConfig($config), Parent Function: none, Related Function: parse

## Admin Header Functions

Function Name: getCurrentPageName, Function Line: 14, File Path: pixels/public/admin/includes/header.php, Description: Get current page name from script filename for navigation highlighting, Parameters: none, Return Type: string, Example Usage: $page = getCurrentPageName(), Parent Function: none, Related Function: isNavActive

Function Name: isNavActive, Function Line: 21, File Path: pixels/public/admin/includes/header.php, Description: Check if navigation item should be marked as active based on current page, Parameters: $page, Return Type: string, Example Usage: $class = isNavActive('index.php'), Parent Function: none, Related Function: getCurrentPageName

Function Name: renderAdminNavigation, Function Line: 28, File Path: pixels/public/admin/includes/header.php, Description: Render admin navigation menu with proper active states and scraper link, Parameters: none, Return Type: void, Example Usage: renderAdminNavigation(), Parent Function: renderAdminHeader, Related Function: isNavActive

Function Name: renderAdminHeader, Function Line: 56, File Path: pixels/public/admin/includes/header.php, Description: Render complete admin header with navigation and flash messages, Parameters: $pageTitle, Return Type: void, Example Usage: renderAdminHeader('Dashboard'), Parent Function: none, Related Function: renderAdminNavigation

## Admin Footer Functions

Function Name: renderAdminFooter, Function Line: 14, File Path: pixels/public/admin/includes/footer.php, Description: Render complete admin footer and close HTML tags, Parameters: none, Return Type: void, Example Usage: renderAdminFooter(), Parent Function: none, Related Function: renderAdminHeaderPagination($page, $totalPages, '/posts'), Parent Function: none, Related Function: none