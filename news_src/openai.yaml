site_name: "OpenAI News"
source_url: "https://openai.com/news/?display=list"
selectors:
  title: "a[href*='/'] h3, a[href*='/'] h2"
  article_url: "a[href*='/']"
  thumbnail: "img[src*='ctfassets.net']"
description: "Configuration for scraping OpenAI news articles from their official news page"
notes: |
  This configuration targets the OpenAI news page structure:
  - Articles are contained in links with href attributes
  - Titles are in h2 or h3 elements within those links
  - Thumbnails use Contentful CDN (ctfassets.net)
  - The page uses a card-based layout for articles
examples:
  - title: "<PERSON> <PERSON> <PERSON><PERSON> introduce io"
    url: "https://openai.com/sam-and-jony/"
    thumbnail: "https://images.ctfassets.net/kftzwdyauwt9/15kshpgxK5cIDIeI2eHCCI/70cf68c982fc0da3bae51e6388a56c37/CMD_250406_VIRGO_001_346_05a-BW-1200x630.jpg?w=640&q=90"
  - title: "Introducing Codex"
    url: "https://openai.com/index/introducing-codex/"
    thumbnail: null
